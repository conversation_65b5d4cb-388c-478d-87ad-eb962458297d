"""
方案生成Agent，负责生成营销方案
支持流式输出，提升用户体验
"""

import json
from typing import Dict, Any, Optional, Callable
from app.agents.base import LLMAgent, AgentResult, agentLogDecorator
from app.utils.models import PlanDraft, MarketingState
from app.llm.LlmFactory import getDefaultLLM
from app.agents.prompts.plan_creator_prompts import PlanCreatorPrompts


class PlanCreatorAgent(LLMAgent):
    """
    方案生成Agent

    负责根据意图和历史方案生成营销方案
    支持流式输出，实时显示生成过程
    """

    def __init__(self):
        """初始化方案生成Agent"""
        super().__init__("PlanCreatorAgent", getDefaultLLM())
        self.stream_callback = None  # 流式回调函数
    
    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """
        方案生成Agent核心逻辑

        Args:
            state: 当前状态

        Returns:
            MarketingState: 更新后的状态
        """
        try:
            # 获取意图和历史方案信息
            intentInfo = state.get("intentInfo")
            ragContext = state.get("ragContext")

            if not intentInfo:
                return state

            # 使用LLM生成方案
            planDraft = self._generatePlanWithLLM(intentInfo, ragContext)

            if not planDraft:
                return state

            # 更新状态
            state.update({
                "planDraft": planDraft,
                "currentStep": "plan_generation_completed"
            })

            return state

        except Exception as e:
            self.logger.error(f"方案生成Agent执行失败: {str(e)}")
            return state

    def executeWithStreaming(self, state: MarketingState, callback: Optional[Callable] = None) -> MarketingState:
        """
        支持流式输出的方案生成Agent执行方法

        Args:
            state: 当前状态
            callback: 流式回调函数，用于实时返回生成过程

        Returns:
            MarketingState: 更新后的状态
        """
        try:
            # 获取意图和历史方案信息
            intentInfo = state.get("intentInfo")
            ragContext = state.get("ragContext")

            if not intentInfo:
                return state

            # 使用流式LLM生成方案
            planDraft = self.generatePlanWithStreaming(intentInfo, ragContext, callback)

            if not planDraft:
                return state

            # 更新状态
            state.update({
                "planDraft": planDraft,
                "currentStep": "plan_generation_completed"
            })

            return state

        except Exception as e:
            self.logger.error(f"流式方案生成Agent执行失败: {str(e)}")
            return state
    
    def _generatePlanWithLLM(self, intentInfo, ragContext) -> PlanDraft:
        """
        使用LLM生成营销方案

        Args:
            intentInfo: 意图信息
            ragContext: 历史方案上下文

        Returns:
            PlanDraft: 生成的方案草稿
        """
        try:
            # 构造prompt
            prompt = PlanCreatorPrompts.get_plan_generation_prompt().format(
                intentInfo=json.dumps(intentInfo, ensure_ascii=False),
                ragContext=json.dumps(ragContext, ensure_ascii=False)
            )

            # 调用LLM
            if not self.llm:
                raise ValueError("LLM实例未设置")

            response = self.llm.invoke(prompt)

            # 解析响应
            if hasattr(response, 'content'):
                responseText = response.content
            else:
                responseText = str(response)

            # 尝试解析JSON
            try:
                planData = json.loads(responseText)
            except json.JSONDecodeError:
                # 如果不是JSON，直接使用文本内容
                planData = {
                    "content": responseText,
                    "outline": []
                }

            # 创建PlanDraft对象
            planDraft = {
                "content": planData.get("content", responseText),
                "outline": planData.get("outline", [])
            }

            return planDraft

        except Exception as e:
            self.logger.error(f"LLM方案生成失败: {str(e)}")
            # 返回默认的PlanDraft，而不是None
            return {
                "content": f"方案生成失败: {str(e)}",
                "outline": ["错误处理"]
            }

    def generatePlanWithStreaming(self, intentInfo, ragContext, callback=None) -> PlanDraft:
        """
        使用流式输出生成营销方案，支持实时反馈

        Args:
            intentInfo: 意图信息
            ragContext: 历史方案上下文
            callback: 流式回调函数，用于实时返回生成过程

        Returns:
            PlanDraft: 生成的方案草稿
        """
        try:
            # 构造prompt
            prompt = PlanCreatorPrompts.get_plan_generation_prompt().format(
                intentInfo=json.dumps(intentInfo, ensure_ascii=False),
                ragContext=json.dumps(ragContext, ensure_ascii=False)
            )

            # 调用LLM
            if not self.llm:
                raise ValueError("LLM实例未设置")

            # 发送思考开始事件
            if callback:
                callback({
                    "type": "thinking",
                    "phase": "start",
                    "message": "开始分析营销需求...",
                    "agent": "PLAN_CREATOR_AGENT"
                })

            # 收集完整响应
            full_response = ""

            # 使用流式调用
            for chunk in self.llm.stream(prompt):
                if hasattr(chunk, 'content') and chunk.content:
                    # 累积完整响应
                    full_response += chunk.content

                    # 发送生成中事件
                    if callback:
                        callback({
                            "type": "generating",
                            "content": chunk.content,
                            "agent": "PLAN_CREATOR_AGENT"
                        })

            # 尝试解析JSON
            try:
                planData = json.loads(full_response)
            except json.JSONDecodeError:
                # 如果不是JSON，直接使用文本内容
                planData = {
                    "content": full_response,
                    "outline": []
                }

            # 创建PlanDraft对象
            planDraft = {
                "content": planData.get("content", full_response),
                "outline": planData.get("outline", [])
            }

            # 发送完成事件
            if callback:
                callback({
                    "type": "complete",
                    "message": "方案生成完成",
                    "agent": "PLAN_CREATOR_AGENT"
                })

            return planDraft

        except Exception as e:
            self.logger.error(f"流式方案生成失败: {str(e)}")

            # 发送错误事件
            if callback:
                callback({
                    "type": "error",
                    "message": f"方案生成失败: {str(e)}",
                    "agent": "PLAN_CREATOR_AGENT"
                })

            # 返回默认的PlanDraft，而不是None
            return {
                "content": f"方案生成失败: {str(e)}",
                "outline": ["错误处理"]
            }