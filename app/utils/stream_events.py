"""
流式事件类型定义和工具函数

定义了完整的流式事件体系，提供更好的用户体验和前端展示效果。

Author: Marketing AI Team
Version: 2.0.0
Date: 2025-07-21
"""

from typing import Dict, Any, Optional
from datetime import datetime
import uuid


class BusinessContentTypes:
    """
    业务内容类型常量 - 用于MESSAGE事件的contentType字段标识

    这些类型不作为独立事件发送，而是通过MESSAGE事件的data.contentType来标识
    """
    BUSINESS_INSIGHT = "businessInsight"        # 通用业务洞察（路由分析等）
    CUSTOMER_ANALYSIS = "customerAnalysis"      # 客群分析结果
    MARKETING_STRATEGY = "marketingStrategy"    # 营销策略建议
    PLAN_RECOMMENDATION = "planRecommendation"  # 方案推荐
    USER_GUIDANCE = "userGuidance"              # 用户引导


class StreamEventTypes:
    """
    流式事件类型常量定义 - 面向业务的三层事件架构

    设计理念：
    1. 工作流级别 - 整体进度感知
    2. 节点级别 - 步骤进度展示
    3. 业务级别 - Markdown格式的核心价值输出

    事件结构：content + data双字段
    - content: 用户可读内容（支持Markdown格式）
    - data: 结构化数据（程序处理的详细信息）
    """

    # === 工作流级别事件 ===
    WORKFLOW_STARTED = "workflowStarted"      # 工作流开始执行
    WORKFLOW_PROGRESS = "workflowProgress"    # 工作流执行进度（新增）
    WORKFLOW_FINISHED = "workflowFinished"    # 工作流执行结束

    # === 节点级别事件 ===
    NODE_STARTED = "nodeStarted"              # 节点开始执行
    NODE_FINISHED = "nodeFinished"            # 节点执行结束

    # === 业务级别事件（核心价值输出）===
    BUSINESS_INSIGHT = "businessInsight"      # 通用业务洞察
    CUSTOMER_ANALYSIS = "customerAnalysis"    # 客群分析结果（新增）
    MARKETING_STRATEGY = "marketingStrategy"  # 营销策略建议（新增）
    PLAN_RECOMMENDATION = "planRecommendation" # 方案推荐（新增）
    USER_GUIDANCE = "userGuidance"            # 用户引导和交互

    # === 内容流式事件（可选保留）===
    MESSAGE = "message"
    MESSAGE_END = "messageEnd"

    # === 系统级别事件 ===
    ERROR = "error"                           # 错误事件
    PING = "ping"                             # 保活事件（保留用于连接检测）



class WorkflowStatus:
    """工作流执行状态常量"""
    RUNNING = "running"                       # 执行中
    SUCCEEDED = "succeeded"                   # 执行成功
    FAILED = "failed"                         # 执行失败
    INTERRUPTED = "interrupted"               # 执行中断（需要用户输入）
    STOPPED = "stopped"                       # 手动停止


class NodeStatus:
    """节点执行状态常量"""
    RUNNING = "running"                       # 执行中
    SUCCEEDED = "succeeded"                   # 执行成功
    FAILED = "failed"                         # 执行失败
    STOPPED = "stopped"                       # 停止执行


class GuidanceType:
    """用户引导类型常量"""
    CONVERSATIONAL = "conversational"        # 对话式引导
    TAGS = "tags"                            # 标签选择
    FORM = "form"                            # 表单填写
    MIXED = "mixed"                          # 混合模式


class StreamEventBuilder:
    """
    流式事件构建器 - 统一事件结构

    提供标准化的事件创建方法，确保所有事件都使用content + data双字段结构。
    """

    @staticmethod
    def createBaseEvent(
        event: str,
        messageId: str,
        conversationId: str,
        content: str = "",
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建基础事件结构

        Args:
            event: 事件类型
            messageId: 消息ID
            conversationId: 对话ID
            content: 文本内容（用户可读）
            data: 结构化数据（程序处理）

        Returns:
            Dict[str, Any]: 标准化的事件数据
        """
        return {
            "event": event,
            "messageId": messageId,
            "conversationId": conversationId,
            "content": content,
            "data": data,
            "createdAt": int(datetime.now().timestamp())
        }
    
    @staticmethod
    def createMessageEvent(
        messageId: str,
        conversationId: str,
        content: str,
        contentType: str = None,
        nodeId: str = None,
        runId: str = None
    ) -> Dict[str, Any]:
        """
        创建消息事件（LLM文本流）- 支持业务内容类型标识和runId

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            content: LLM生成的文本内容
            contentType: 业务内容类型（customerAnalysis, marketingStrategy, planRecommendation等）
            nodeId: 当前执行的节点ID
            runId: LLM运行ID（同一批流式输出共享相同的runId）

        Returns:
            Dict[str, Any]: 消息事件
        """
        data = {}
        if contentType:
            data["contentType"] = contentType
        if nodeId:
            data["nodeId"] = nodeId

        # 创建基础事件
        event = StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.MESSAGE,
            messageId=messageId,
            conversationId=conversationId,
            content=content,
            data=data if data else None
        )

        # 将runId添加到顶层，与messageId同级
        if runId:
            event["runId"] = runId

        return event

    @staticmethod
    def createWorkflowStartedEvent(
        messageId: str,
        conversationId: str,
        totalSteps: int = 5
    ) -> Dict[str, Any]:
        """
        创建工作流开始事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            totalSteps: 总步数

        Returns:
            Dict[str, Any]: 工作流开始事件
        """
        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.WORKFLOW_STARTED,
            messageId=messageId,
            conversationId=conversationId,
            content="## 🚀 开始执行营销工作流",
            data={
                "totalSteps": totalSteps,
                "workflowType": "marketing"
            }
        )

    @staticmethod
    def createWorkflowFinishedEvent(
        messageId: str,
        conversationId: str,
        status: str,
        totalSteps: int = 5,
        completedSteps: int = 5,
        elapsedTime: float = 0.0,
        error: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建工作流结束事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            status: 执行状态（succeeded/failed/interrupted）
            totalSteps: 总步数
            completedSteps: 已完成步数
            elapsedTime: 执行耗时（秒）
            error: 错误信息

        Returns:
            Dict[str, Any]: 工作流结束事件
        """
        if status == WorkflowStatus.SUCCEEDED:
            content = "## ✅ 工作流执行完成"
        elif status == WorkflowStatus.FAILED:
            content = "## ❌ 工作流执行失败"
        elif status == WorkflowStatus.INTERRUPTED:
            content = "## ⏸️ 工作流已中断，等待用户输入"
        else:
            content = f"## 🔄 工作流执行{status}"

        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.WORKFLOW_FINISHED,
            messageId=messageId,
            conversationId=conversationId,
            content=content,
            data={
                "status": status,
                "totalSteps": totalSteps,
                "completedSteps": completedSteps,
                "elapsedTime": elapsedTime,
                "error": error,
                "successRate": completedSteps / totalSteps if totalSteps > 0 else 0
            }
        )

    @staticmethod
    def createNodeStartedEvent(
        messageId: str,
        conversationId: str,
        nodeId: str,
        stepNumber: int,
        totalSteps: int = 5
    ) -> Dict[str, Any]:
        """
        创建节点开始事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            nodeId: 节点ID
            stepNumber: 步骤编号
            totalSteps: 总步数

        Returns:
            Dict[str, Any]: 节点开始事件
        """
        displayName = getNodeDisplayName(nodeId)
        progressPercent = int((stepNumber / totalSteps) * 100)

        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.NODE_STARTED,
            messageId=messageId,
            conversationId=conversationId,
            content=f"## 🔄 开始执行**{displayName}**",
            data={
                "nodeId": nodeId,
                "stepNumber": stepNumber,
                "totalSteps": totalSteps,
                "progressPercent": progressPercent,
                "displayName": displayName
            }
        )

    @staticmethod
    def createNodeFinishedEvent(
        messageId: str,
        conversationId: str,
        nodeId: str,
        status: str,
        stepNumber: int,
        elapsedTime: float = 0.0,
        outputs: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建节点完成事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            nodeId: 节点ID
            status: 执行状态
            stepNumber: 步骤编号
            elapsedTime: 执行耗时
            outputs: 输出数据
            error: 错误信息

        Returns:
            Dict[str, Any]: 节点完成事件
        """
        displayName = getNodeDisplayName(nodeId)

        if status == NodeStatus.SUCCEEDED:
            content = f"## ✅ **{displayName}**执行完成"
        elif status == NodeStatus.FAILED:
            content = f"## ❌ **{displayName}**执行失败"
        else:
            content = f"## 🔄 **{displayName}**执行{status}"

        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.NODE_FINISHED,
            messageId=messageId,
            conversationId=conversationId,
            content=content,
            data={
                "nodeId": nodeId,
                "status": status,
                "stepNumber": stepNumber,
                "elapsedTime": elapsedTime,
                "outputs": outputs or {},
                "error": error,
                "displayName": displayName
            }
        )

    @staticmethod
    def createBusinessInsightEvent(
        messageId: str,
        conversationId: str,
        nodeId: str,
        insightType: str,
        insightData: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建业务洞察事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            nodeId: 节点ID
            insightType: 洞察类型
            insightData: 洞察数据

        Returns:
            Dict[str, Any]: 业务洞察事件
        """
        displayName = getNodeDisplayName(nodeId)

        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.BUSINESS_INSIGHT,
            messageId=messageId,
            conversationId=conversationId,
            content=f"{displayName}分析结果",
            data={
                "nodeId": nodeId,
                "insightType": insightType,
                "displayName": displayName,
                **insightData
            }
        )

    @staticmethod
    def createUserGuidanceEvent(
        messageId: str,
        conversationId: str,
        guidanceType: str,
        guidanceMessage: str,
        missingFields: Optional[list[str]] = None,
        interactionData: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建用户引导事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            guidanceType: 引导类型
            guidanceMessage: 引导消息
            missingFields: 缺失字段列表
            interactionData: 交互数据

        Returns:
            Dict[str, Any]: 用户引导事件
        """
        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.USER_GUIDANCE,
            messageId=messageId,
            conversationId=conversationId,
            content=guidanceMessage,
            data={
                "guidanceType": guidanceType,
                "missingFields": missingFields or [],
                "interactionData": interactionData or {}
            }
        )

    @staticmethod
    def createMessageEndEvent(
        messageId: str,
        conversationId: str,
        totalEvents: int = 0,
        executionTime: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None,
        contentType: str = None,
        nodeId: str = None,
        fullContent: str = None,
        runId: str = None
    ) -> Dict[str, Any]:
        """
        创建消息结束事件 - 支持业务内容类型标识和runId

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            totalEvents: 总事件数
            executionTime: 执行时间
            metadata: 元数据
            contentType: 业务内容类型
            nodeId: 节点ID
            fullContent: 完整内容（用于前端验证）
            runId: LLM运行ID（同一批流式输出共享相同的runId）

        Returns:
            Dict[str, Any]: 消息结束事件
        """
        data = {
            "totalEvents": totalEvents,
            "executionTime": executionTime,
            "metadata": metadata or {}
        }

        if contentType:
            data["contentType"] = contentType
        if nodeId:
            data["nodeId"] = nodeId
        if fullContent:
            data["fullContent"] = fullContent
            data["totalLength"] = len(fullContent)

        # 创建基础事件
        event = StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.MESSAGE_END,
            messageId=messageId,
            conversationId=conversationId,
            content="回复生成完成",
            data=data
        )

        # 将runId添加到顶层，与messageId同级
        if runId:
            event["runId"] = runId

        return event

    @staticmethod
    def createErrorEvent(
        messageId: str,
        conversationId: str,
        errorCode: str,
        errorMessage: str,
        canRetry: bool = True,
        retryCount: int = 0,
        maxRetries: int = 3
    ) -> Dict[str, Any]:
        """
        创建错误事件

        Args:
            messageId: 消息ID
            conversationId: 对话ID
            errorCode: 错误码
            errorMessage: 错误消息
            canRetry: 是否可重试
            retryCount: 重试次数
            maxRetries: 最大重试次数

        Returns:
            Dict[str, Any]: 错误事件
        """
        return StreamEventBuilder.createBaseEvent(
            event=StreamEventTypes.ERROR,
            messageId=messageId,
            conversationId=conversationId,
            content=f"## ❌ 执行错误\n\n**错误信息**: {errorMessage}",
            data={
                "errorCode": errorCode,
                "canRetry": canRetry,
                "retryCount": retryCount,
                "maxRetries": maxRetries,
                "status": 500
            }
        )

    @staticmethod
    def createPingEvent() -> Dict[str, Any]:
        """
        创建保活事件

        Returns:
            Dict[str, Any]: 保活事件
        """
        return {
            "event": StreamEventTypes.PING,
            "createdAt": int(datetime.now().timestamp())
        }
    



# === 工具函数 ===

def generateMessageId() -> str:
    """生成消息ID"""
    return f"msg_{str(uuid.uuid4())[:8]}_{int(datetime.now().timestamp())}"


def getNodeDisplayName(nodeId: str) -> str:
    """
    获取节点的用户友好显示名称

    Args:
        nodeId: 节点ID

    Returns:
        str: 显示名称
    """
    displayNames = {
        "ROUTER_AGENT": "智能路由",
        "INTENTION_AGENT": "意图识别",
        "TAG_CUSTOMER_AGENT": "客群分析",
        "HISTORY_RAG_AGENT": "历史数据检索",
        "PLAN_CREATOR_AGENT": "方案生成",
        "COMPLETE_INTENT_NODE": "意图补全",
        "COMPLETE_CUSTOMER_NODE": "客群补全"
    }
    return displayNames.get(nodeId, nodeId)


def getNodeIndex(nodeId: str) -> int:
    """
    获取节点在工作流中的执行序号

    Args:
        nodeId: 节点ID

    Returns:
        int: 执行序号
    """
    indexMapping = {
        "ROUTER_AGENT": 1,
        "INTENTION_AGENT": 2,
        "TAG_CUSTOMER_AGENT": 3,
        "HISTORY_RAG_AGENT": 4,
        "PLAN_CREATOR_AGENT": 5,
        "COMPLETE_INTENT_NODE": 2,
        "COMPLETE_CUSTOMER_NODE": 3
    }
    return indexMapping.get(nodeId, 1)


def getInsightType(nodeId: str) -> str:
    """
    获取业务洞察类型

    Args:
        nodeId: 节点ID

    Returns:
        str: 洞察类型
    """
    typeMapping = {
        "ROUTER_AGENT": "routingAnalysis",
        "INTENTION_AGENT": "intentionAnalysis",
        "TAG_CUSTOMER_AGENT": "customerAnalysis",
        "HISTORY_RAG_AGENT": "historicalData",
        "PLAN_CREATOR_AGENT": "planRecommendation"
    }
    return typeMapping.get(nodeId, "general")
