#!/usr/bin/env python3
"""
测试最终的thinking内容过滤功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_thinking_filter():
    """测试thinking内容过滤功能"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试thinking内容过滤功能 ===\n")
    
    # 测试用例1: 完整的thinking块
    print("测试1: 完整的thinking块")
    workflow._resetThinkingState()
    
    inputs1 = [
        "<thinking>",
        "这是思考内容",
        "</thinking>",
        "这是JSON内容"
    ]
    
    for i, inp in enumerate(inputs1):
        result = workflow._filterThinkingContent(inp)
        print(f"  输入: '{inp}' -> 输出: '{result}'")
    
    # 测试用例2: 分段的thinking标签
    print("\n测试2: 分段的thinking标签")
    workflow._resetThinkingState()
    
    inputs2 = [
        "<th",
        "inking>",
        "分段的思考内容",
        "</th",
        "inking>"
    ]
    
    for i, inp in enumerate(inputs2):
        result = workflow._filterThinkingContent(inp)
        print(f"  输入: '{inp}' -> 输出: '{result}'")
    
    # 测试用例3: 非thinking内容
    print("\n测试3: 非thinking内容")
    workflow._resetThinkingState()
    
    inputs3 = [
        "普通文本",
        '{"goal": "test"}',
        "更多普通文本"
    ]
    
    for i, inp in enumerate(inputs3):
        result = workflow._filterThinkingContent(inp)
        print(f"  输入: '{inp}' -> 输出: '{result}'")

if __name__ == "__main__":
    print("开始测试thinking内容过滤功能...\n")
    
    try:
        test_thinking_filter()
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
