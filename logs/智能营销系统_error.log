2025-07-07 12:40:58 | ERROR    | logging:callHandlers:1762 | 未处理的异常: 'MarketingRoutes' object has no attribute 'logger'
  + Exception Group Traceback (most recent call last):
  |
  |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 177, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x00000238476AC770>
  |                │     └ <function create_task_group at 0x00000238434DA020>
  |                └ <module 'anyio' from 'D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\venv\\Lib\\site-packages\\anyio\\__init__.py'>
  |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000238476C4AE0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000023847705...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000238475EFBC0>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x00000238435C7D80>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [AttributeError("'MarketingRoutes' object has no attribute 'logger'")])
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x000002384762A810>
    |     └ <contextlib._GeneratorContextManager object at 0x00000238476AC7D0>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000238475DAA20>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x00000238476AC080>
    |                      │    └ <function loggingMiddleware at 0x0000023847662160>
    |                      └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>
    |
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\api\app.py", line 240, in loggingMiddleware
    |     response = await call_next(request)
    |                      │         └ <starlette.middleware.base._CachedRequest object at 0x00000238476AC080>
    |                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000238475DAA20>
    |
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    |     raise app_exc
    |           └ AttributeError("'MarketingRoutes' object has no attribute 'logger'")
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |           │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000023847712660>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
    |           └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': '127.0.0.1:8000', 'connection': 'keep-alive', 'content-length': '80', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000023847712660>
    |           │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x0000023844710400>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60...
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000238475EFD10>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |           │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60...
    |           │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    │    └ <starlette.requests.Request object at 0x0000023847705AC0>
    |           │                            │    └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
    |           │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000238475EFD10>
    |           └ <function wrap_app_handling_exceptions at 0x00000238436D7A60>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
    |           │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000238475EEBA0>>
    |           └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 734, in app
    |     await route.handle(scope, receive, send)
    |           │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
    |           │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │     └ <function Route.handle at 0x0000023843700F40>
    |           └ APIRoute(path='/api/marketing/chat', name='marketing_chat', methods=['POST'])
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
    |           │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function request_response.<locals>.app at 0x00000238476627A0>
    |           └ APIRoute(path='/api/marketing/chat', name='marketing_chat', methods=['POST'])
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |           │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
    |           │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │                            │    └ <starlette.requests.Request object at 0x0000023847705C10>
    |           │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000023847712700>
    |           └ <function wrap_app_handling_exceptions at 0x00000238436D7A60>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |           │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712840>
    |           │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
    |           │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           └ <function request_response.<locals>.app.<locals>.app at 0x0000023847712700>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 73, in app
    |     response = await f(request)
    |                      │ └ <starlette.requests.Request object at 0x0000023847705C10>
    |                      └ <function get_request_handler.<locals>.app at 0x0000023847662840>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    |     raw_response = await run_endpoint_function(
    |                          └ <function run_endpoint_function at 0x0000023843700A40>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\fastapi\routing.py", line 214, in run_endpoint_function
    |     return await run_in_threadpool(dependant.call, **values)
    |                  │                 │         │       └ {'request': MarketingChatRequest(userInput='我想做一场暑期促销活动', userId='user_12345', conversationId=None, sessionId=None)}
    |                  │                 │         └ <bound method MarketingRoutes.marketing_chat of <app.api.routes.MarketingRoutes object at 0x0000023847443E00>>
    |                  │                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...
    |                  └ <function run_in_threadpool at 0x0000023843469620>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    |     return await anyio.to_thread.run_sync(func)
    |                  │     │         │        └ functools.partial(<bound method MarketingRoutes.marketing_chat of <app.api.routes.MarketingRoutes object at 0x0000023847443E0...
    |                  │     │         └ <function run_sync at 0x00000238434698A0>
    |                  │     └ <module 'anyio.to_thread' from 'D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\venv\\Lib\\site-packages\\anyio\\to_thread.py'>
    |                  └ <module 'anyio' from 'D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\venv\\Lib\\site-packages\\anyio\\__init__.py'>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\to_thread.py", line 56, in run_sync
    |     return await get_async_backend().run_sync_in_worker_thread(
    |                  └ <function get_async_backend at 0x000002384346A5C0>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 2470, in run_sync_in_worker_thread
    |     return await future
    |                  └ <Future finished exception=AttributeError("'MarketingRoutes' object has no attribute 'logger'")>
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 967, in run
    |     result = context.run(func, *args)
    |
    |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\api\routes.py", line 211, in marketing_chat
    |     self.logger.error(f"工作流执行失败: {str(e)}")
    |     └ <app.api.routes.MarketingRoutes object at 0x0000023847443E00>
    |
    | AttributeError: 'MarketingRoutes' object has no attribute 'logger'
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 880
               │     └ 3
               └ <function _main at 0x0000023842DDEE80>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 880
           │    └ <function BaseProcess._bootstrap at 0x0000023842DB9B20>
           └ <SpawnProcess name='SpawnProcess-1' parent=37780 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000023842DB9080>
    └ <SpawnProcess name='SpawnProcess-1' parent=37780 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000023842FBC4A0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=37780 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=37780 started>
    │    └ <function subprocess_started at 0x0000023842F77060>
    └ <SpawnProcess name='SpawnProcess-1' parent=37780 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1072, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000023842FBD0A0>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1072, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000023842F763E0>
           │       │   └ <uvicorn.server.Server object at 0x0000023842FBD0A0>
           │       └ <function run at 0x0000023842C9B1A0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000023842DD0820>
           │      └ <function Runner.run at 0x0000023842CF6B60>
           └ <asyncio.runners.Runner object at 0x0000023842F63590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000023842CF4720>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000023842F63590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000023842CF4680>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000023842CF6480>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000023842C82660>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\protocols\http\h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023842FBE330>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000238477058E0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000023847705...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x00000238436E5A00>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000023842FBE330>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000238477058E0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000023847705...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x00000238477058E0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000023847705...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000238475EFBC0>
          └ <fastapi.applications.FastAPI object at 0x00000238436E5A00>
> File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x00000238476C4AE0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.h11_impl.RequestResponseCycle object at 0x0000023847705...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x00000238475EFBC0>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 176, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x00000238435C7D80>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [AttributeError("'MarketingRoutes' object has no attribute 'logger'")])
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x000002384762A810>
    └ <contextlib._GeneratorContextManager object at 0x00000238476AC7D0>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_utils.py", line 82, in collapse_excgroups
    raise exc
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000238475DAA20>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x00000238476AC080>
                     │    └ <function loggingMiddleware at 0x0000023847662160>
                     └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>

  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\api\app.py", line 240, in loggingMiddleware
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x00000238476AC080>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x00000238475DAA20>

  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 156, in call_next
    raise app_exc
          └ AttributeError("'MarketingRoutes' object has no attribute 'logger'")
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000023847712660>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000238475EF890>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': '127.0.0.1:8000', 'connection': 'keep-alive', 'content-length': '80', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000023847712660>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000023844710400>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000238475EFD10>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x00000238475EFE60...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000023847705AC0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000238475EFD10>
          └ <function wrap_app_handling_exceptions at 0x00000238436D7A60>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000238475EEBA0>>
          └ <fastapi.routing.APIRouter object at 0x00000238475EEBA0>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │     └ <function Route.handle at 0x0000023843700F40>
          └ APIRoute(path='/api/marketing/chat', name='marketing_chat', methods=['POST'])
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000238476627A0>
          └ APIRoute(path='/api/marketing/chat', name='marketing_chat', methods=['POST'])
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712480>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000023847705C10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000023847712700>
          └ <function wrap_app_handling_exceptions at 0x00000238436D7A60>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000023847712840>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000238475DA340>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000023847712700>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000023847705C10>
                     └ <function get_request_handler.<locals>.app at 0x0000023847662840>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000023843700A40>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\fastapi\routing.py", line 214, in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
                 │                 │         │       └ {'request': MarketingChatRequest(userInput='我想做一场暑期促销活动', userId='user_12345', conversationId=None, sessionId=None)}
                 │                 │         └ <bound method MarketingRoutes.marketing_chat of <app.api.routes.MarketingRoutes object at 0x0000023847443E00>>
                 │                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...
                 └ <function run_in_threadpool at 0x0000023843469620>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\starlette\concurrency.py", line 37, in run_in_threadpool
    return await anyio.to_thread.run_sync(func)
                 │     │         │        └ functools.partial(<bound method MarketingRoutes.marketing_chat of <app.api.routes.MarketingRoutes object at 0x0000023847443E0...
                 │     │         └ <function run_sync at 0x00000238434698A0>
                 │     └ <module 'anyio.to_thread' from 'D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\venv\\Lib\\site-packages\\anyio\\to_thread.py'>
                 └ <module 'anyio' from 'D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\venv\\Lib\\site-packages\\anyio\\__init__.py'>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\to_thread.py", line 56, in run_sync
    return await get_async_backend().run_sync_in_worker_thread(
                 └ <function get_async_backend at 0x000002384346A5C0>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 2470, in run_sync_in_worker_thread
    return await future
                 └ <Future finished exception=AttributeError("'MarketingRoutes' object has no attribute 'logger'")>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\anyio\_backends\_asyncio.py", line 967, in run
    result = context.run(func, *args)

  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\api\routes.py", line 211, in marketing_chat
    self.logger.error(f"工作流执行失败: {str(e)}")
    └ <app.api.routes.MarketingRoutes object at 0x0000023847443E00>

AttributeError: 'MarketingRoutes' object has no attribute 'logger'
2025-07-07 12:42:29 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:29 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:30 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:31 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:31 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:32 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:32 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:33 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:33 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:34 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:34 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:34 | ERROR    | logging:callHandlers:1762 | 意图识别Agent执行失败: 'HumanMessage' object has no attribute 'get'
2025-07-07 12:42:35 | ERROR    | logging:callHandlers:1762 | 工作流执行失败: Recursion limit of 25 reached without hitting a stop condition. You can increase the limit by setting the `recursion_limit` config key.
For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT
2025-07-07 13:40:53 | ERROR    | logging:callHandlers:1762 | 工作流执行失败: name 'AIMessage' is not defined
2025-07-07 14:12:44 | ERROR    | logging:callHandlers:1762 | 工作流执行失败: name 'AIMessage' is not defined
2025-07-07 14:15:58 | ERROR    | app.api.routes:marketing_chat:214 | 工作流执行失败: name 'AIMessage' is not defined
2025-07-07 14:29:30 | ERROR    | app.agents.RouterAgent:_llmRoutingDecision:278 | LLM路由决策失败: 'dict' object has no attribute 'goal'
2025-07-07 14:29:30 | ERROR    | app.agents.RouterAgent:_determineNextStep:243 | 路由决策异常: 'dict' object has no attribute 'missingFields'，使用兜底逻辑
2025-07-07 14:29:30 | ERROR    | app.agents.RouterAgent:execute:122 | 路由Agent执行失败: 'dict' object has no attribute 'missingFields'
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:_llmRoutingDecision:278 | LLM路由决策失败: 'dict' object has no attribute 'goal'
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:_determineNextStep:243 | 路由决策异常: 'dict' object has no attribute 'missingFields'，使用兜底逻辑
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:execute:122 | 路由Agent执行失败: 'dict' object has no attribute 'missingFields'
2025-07-07 14:29:35 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:106 | LLM客群分析失败: 'dict' object has no attribute 'json'
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:_llmRoutingDecision:278 | LLM路由决策失败: 'dict' object has no attribute 'goal'
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:_determineNextStep:243 | 路由决策异常: 'dict' object has no attribute 'missingFields'，使用兜底逻辑
2025-07-07 14:29:35 | ERROR    | app.agents.RouterAgent:execute:122 | 路由Agent执行失败: 'dict' object has no attribute 'missingFields'
2025-07-07 18:01:51 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:106 | LLM客群分析失败: 'dict' object has no attribute 'json'
2025-07-07 19:40:05 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:106 | LLM客群分析失败: 'dict' object has no attribute 'json'
2025-07-08 15:38:57 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:111 | LLM客群分析失败: 'intentInfo'
2025-07-08 15:51:33 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:111 | LLM客群分析失败: 'intentInfo'
2025-07-08 15:58:37 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:111 | LLM客群分析失败: 'intentInfo'
2025-07-14 10:23:55 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 10:26:31 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 10:27:18 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:108 | LLM客群分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 10:27:39 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 15:48:40 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: Too much data for declared Content-Length
2025-07-14 15:49:37 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:34:16 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: Too much data for declared Content-Length
2025-07-14 20:34:32 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: Too much data for declared Content-Length
2025-07-14 20:38:57 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: 'Request' object has no attribute 'userId'
2025-07-14 20:38:57 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: Too much data for declared Content-Length
2025-07-14 20:39:33 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: 'Request' object has no attribute 'userId'
2025-07-14 20:46:10 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:46:44 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:47:20 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:108 | LLM客群分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:47:35 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:49:21 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:50:18 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:51:06 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:51:47 | ERROR    | app.agents.TagCustomerAgent:_analyzeCustomerWithLLM:108 | LLM客群分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:51:57 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 20:54:16 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 21:07:38 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:336 | LLM意图分析失败: name 'processLLMJsonResponse' is not defined
2025-07-14 21:08:11 | ERROR    | app.api.app:globalExceptionHandler:218 | 未处理的异常: Too much data for declared Content-Length
2025-07-16 17:47:26 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x000001BE7F15AFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x000001BE7F133240>
           └ <SpawnProcess name='SpawnProcess-1' parent=34956 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001BE7F1327A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34956 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001BE7FC96F60>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34956 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=34956 started>
    │    └ <function subprocess_started at 0x000001BE7FC819E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=34956 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=964, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001BE7FC97C50>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=964, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001BE7FC80D60>
           │       │   └ <uvicorn.server.Server object at 0x000001BE7FC97C50>
           │       └ <function run at 0x000001BE7F1BFEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001BE7FC758C0>
           │      └ <function Runner.run at 0x000001BE7FABB240>
           └ <asyncio.runners.Runner object at 0x000001BE7F12D3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001BE7FAB8E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001BE7F12D3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001BE7FAB8D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001BE7FABAB60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE7FA42DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x000001BE0D681260>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '111', 'nextNode': 'INTENTION_AGENT', 'routingC...
    │    └ <weakref at 0x000001BE0F6E9550; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-16 19:10:39 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 372
               │     └ 3
               └ <function _main at 0x0000029C586DAFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 372
           │    └ <function BaseProcess._bootstrap at 0x0000029C58683240>
           └ <SpawnProcess name='SpawnProcess-1' parent=6404 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000029C586827A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=6404 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000029C59227200>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=6404 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=6404 started>
    │    └ <function subprocess_started at 0x0000029C592159E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=6404 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=944, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000029C59244080>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=944, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000029C59214D60>
           │       │   └ <uvicorn.server.Server object at 0x0000029C59244080>
           │       └ <function run at 0x0000029C5873BEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000029C592058C0>
           │      └ <function Runner.run at 0x0000029C5903B240>
           └ <asyncio.runners.Runner object at 0x0000029C59227500>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000029C59038E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000029C59227500>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000029C59038D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000029C5903AB60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000029C58FC2DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x0000029C5B7F1300>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '1212', 'nextNode': 'INTENTION_AGENT', 'routing...
    │    └ <weakref at 0x0000029C5D859550; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-17 00:16:39 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:541 | 路由决策失败: Invalid \escape: line 1 column 147 (char 146)
2025-07-17 00:17:48 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x000001E053BBAFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x000001E053B73240>
           └ <SpawnProcess name='SpawnProcess-1' parent=51120 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E053B727A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=51120 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E0547070E0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=51120 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=51120 started>
    │    └ <function subprocess_started at 0x000001E0546F19E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=51120 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=908, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E054707D40>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=908, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001E0546F0D60>
           │       │   └ <uvicorn.server.Server object at 0x000001E054707D40>
           │       └ <function run at 0x000001E053C1FEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E0546E58C0>
           │      └ <function Runner.run at 0x000001E05451B240>
           └ <asyncio.runners.Runner object at 0x000001E0546DE210>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E054518E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E0546DE210>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E054518D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E05451AB60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E0544A2DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x000001E056CD1440>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '测试流式跨域请求', 'nextNode': 'INTENTION_AGENT', 'rou...
    │    └ <weakref at 0x000001E058EDE750; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-17 00:26:42 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: cannot schedule new futures after interpreter shutdown
2025-07-17 00:32:33 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x00000237FF42AFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x00000237FF403240>
           └ <SpawnProcess name='SpawnProcess-1' parent=30876 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000237FF4027A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=30876 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000237914471A0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=30876 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=30876 started>
    │    └ <function subprocess_started at 0x00000237914359E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=30876 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=880, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000023791447E30>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=880, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000023791434D60>
           │       │   └ <uvicorn.server.Server object at 0x0000023791447E30>
           │       └ <function run at 0x00000237FF48FEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000237914258C0>
           │      └ <function Runner.run at 0x00000237FFD8B240>
           └ <asyncio.runners.Runner object at 0x00000237FF3FD3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000237FFD88E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000237FF3FD3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000237FFD88D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000237FFD8AB60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000237FFD12DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x00000237939553A0>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '我想做一个暑期促销活动，目标是大学生群体，预算2万元', 'nextNode': 'INTE...
    │    └ <weakref at 0x0000023795B3AD50; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-17 00:40:37 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 340
               │     └ 3
               └ <function _main at 0x000001FF5A9BAFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 340
           │    └ <function BaseProcess._bootstrap at 0x000001FF5A973240>
           └ <SpawnProcess name='SpawnProcess-1' parent=24108 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001FF5A9727A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=24108 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001FF5B5073E0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=24108 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=24108 started>
    │    └ <function subprocess_started at 0x000001FF5B4F59E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=24108 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=424, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001FF5B520170>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=424, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x000001FF5B4F4D60>
           │       │   └ <uvicorn.server.Server object at 0x000001FF5B520170>
           │       └ <function run at 0x000001FF5AA1FEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001FF5B4E58C0>
           │      └ <function Runner.run at 0x000001FF5B31B240>
           └ <asyncio.runners.Runner object at 0x000001FF5B507EF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001FF5B318E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001FF5B507EF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001FF5B318D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001FF5B31AB60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001FF5B2A2DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x000001FF5DAD53A0>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '我想做一个暑期促销活动，目标是大学生群体，预算2万元', 'nextNode': 'INTE...
    │    └ <weakref at 0x000001FF5FB39B50; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-17 12:38:47 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 348
               │     └ 3
               └ <function _main at 0x0000021E7F6BAFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 348
           │    └ <function BaseProcess._bootstrap at 0x0000021E7F663240>
           └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000021E7F6627A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000021E05647140>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
    │    └ <function subprocess_started at 0x0000021E056359E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000021E05647E90>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000021E05634D60>
           │       │   └ <uvicorn.server.Server object at 0x0000021E05647E90>
           │       └ <function run at 0x0000021E7F71FEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000021E056258C0>
           │      └ <function Runner.run at 0x0000021E05487240>
           └ <asyncio.runners.Runner object at 0x0000021E05647920>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000021E05484E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000021E05647920>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000021E05484D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000021E05486B60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000021E05412DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...需要意图分析', ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\...
    │    │          │     └ <function _exception at 0x0000021E07BF53A0>
    │    │          └ PregelExecutableTask(name='ROUTER_AGENT', input={'messages': [], 'userInput': '我想做一个暑期促销活动，目标是大学生群体，预算2万元', 'nextNode': 'INTE...
    │    └ <weakref at 0x0000021E09C59CD0; dead>
    └ {<Task finished name='ROUTER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib...

TypeError: 'NoneType' object is not callable
2025-07-17 12:39:52 | ERROR    | logging:callHandlers:1762 | Exception in callback FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)
handle: <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)>
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 348
               │     └ 3
               └ <function _main at 0x0000021E7F6BAFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 348
           │    └ <function BaseProcess._bootstrap at 0x0000021E7F663240>
           └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000021E7F6627A0>
    └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000021E05647140>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
    │    └ <function subprocess_started at 0x0000021E056359E0>
    └ <SpawnProcess name='SpawnProcess-1' parent=32900 started>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000021E05647E90>>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uvicorn\server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=824, family=2, type=1, proto=6, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x0000021E05634D60>
           │       │   └ <uvicorn.server.Server object at 0x0000021E05647E90>
           │       └ <function run at 0x0000021E7F71FEC0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000021E056258C0>
           │      └ <function Runner.run at 0x0000021E05487240>
           └ <asyncio.runners.Runner object at 0x0000021E05647920>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\uv...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000021E05484E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000021E05647920>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000021E05484D60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000021E05486B60>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000021E05412DE0>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle FuturesDict.on_done(PregelExecuta... subgraphs=[]))(<Task finishe...ns': {}, ...}>)>
  File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\runner.py", line 107, in on_done
    self.callback()(task, _exception(fut))  # type: ignore[misc]
    │    │          │     │          └ <Task finished name='TAG_CUSTOMER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\ven...
    │    │          │     └ <function _exception at 0x0000021E07BF53A0>
    │    │          └ PregelExecutableTask(name='TAG_CUSTOMER_AGENT', input={'messages': [], 'userInput': '我想做一个暑期促销活动，目标是大学生群体，预算2万元', 'intentInfo...
    │    └ <weakref at 0x0000021E09D5F250; dead>
    └ {<Task finished name='TAG_CUSTOMER_AGENT' coro=<arun_with_retry() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\ve...

TypeError: 'NoneType' object is not callable
2025-07-17 14:16:42 | ERROR    | logging:callHandlers:1762 | Task was destroyed but it is pending!
task: <Task pending name='Task-36' coro=<AsyncQueue.wait() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langgraph\utils\queue.py:33> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-17 14:16:42 | ERROR    | logging:callHandlers:1762 | Task was destroyed but it is pending!
task: <Task pending name='Task-59' coro=<AsyncQueue.wait() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langgraph\utils\queue.py:33> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-17 14:16:42 | ERROR    | logging:callHandlers:1762 | Task was destroyed but it is pending!
task: <Task pending name='Task-82' coro=<AsyncQueue.wait() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langgraph\utils\queue.py:33> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-07-17 14:19:43 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '6981445a-c072-99ad-b65a-96f7998a77bd'}
2025-07-17 14:19:47 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': 'bba30633-07ce-999d-9c6b-5d3da32e28a1'}
2025-07-17 14:19:47 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:337 | LLM意图分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '5b3eba43-c45f-9045-bf50-cdd9c868aaf5'}
2025-07-17 14:19:49 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': 'a0747e4d-bd7f-9d5c-9283-bf5b2f29c072'}
2025-07-17 14:19:51 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:337 | LLM意图分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '9e50f7f0-29e9-906a-bc96-7609af10b85f'}
2025-07-17 14:19:51 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '8cad49ca-7b70-94ff-975a-26523e7a56a4'}
2025-07-17 14:19:52 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': 'bbe23f06-5d00-9527-8958-c21eff89ba12'}
2025-07-17 14:19:53 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '628515f8-7cf6-9250-9f1b-11359f521303'}
2025-07-17 14:19:54 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '33245fd6-c0e0-94c2-ab85-89ec2c0f7105'}
2025-07-17 14:19:56 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:321 | LLM意图提取失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '61aa0afe-debe-9fb9-9805-61ef49a3f85e'}
2025-07-17 14:19:56 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '66e6d518-cb9a-93a5-a255-476931cc60b7'}
2025-07-17 14:19:58 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:321 | LLM意图提取失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '4fb1c4f1-35d5-97ca-b862-a63616e59412'}
2025-07-17 14:19:58 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '9272d123-bb7c-9489-a0fa-4139ca02eef2'}
2025-07-17 14:19:58 | ERROR    | app.agents.tools.RouterAgentTools:performLlmIntentAnalysis:337 | LLM意图分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '3edf6e41-145b-9597-a639-111708070fd2'}
2025-07-17 14:20:00 | ERROR    | app.agents.IntentionAgent:_generateGuidancePrompts:641 | 全面的引导话术生成失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': '89cf639a-6d2f-9a71-963a-b7d97693acb1'}
2025-07-17 14:20:00 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': 'f28062c9-6093-9dc2-842e-8250c4d862a7'}
2025-07-17 14:20:00 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: Error code: 429 - {'error': {'message': 'You exceeded your current requests list.', 'type': 'limit_requests', 'param': None, 'code': 'limit_requests'}, 'request_id': 'e7e346c8-1fd5-925c-9dd8-143e187e223d'}
2025-07-22 19:49:18 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:675 | 流式工作流执行失败: type object 'StreamEventBuilder' has no attribute 'create_workflow_start_event'
2025-07-22 19:58:42 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:675 | 流式工作流执行失败: type object 'StreamEventBuilder' has no attribute 'create_workflow_start_event'
2025-07-22 20:02:13 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:675 | 流式工作流执行失败: type object 'StreamEventBuilder' has no attribute 'create_workflow_start_event'
2025-07-24 12:38:44 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:597 | 流式执行内部错误: 
2025-07-24 12:41:28 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:597 | 流式执行内部错误: 
2025-07-24 12:41:45 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:597 | 流式执行内部错误: 
2025-07-24 18:02:05 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:596 | 流式执行内部错误: 
2025-07-24 18:30:18 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:245 | ReAct Agent分析失败: peer closed connection without sending complete message body (incomplete chunked read)
2025-07-24 18:30:26 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:321 | LLM意图提取失败: Connection error.
2025-07-24 18:30:33 | ERROR    | app.agents.IntentionAgent:_generateGuidancePrompts:641 | 全面的引导话术生成失败: Connection error.
2025-07-24 18:30:33 | ERROR    | app.api.routes:generateStreamEvents:341 | 事件序列化失败: Object of type Interrupt is not JSON serializable
2025-07-24 18:30:33 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:613 | streamExecutionError: {'errorType': 'AttributeError', 'errorMessage': "'tuple' object has no attribute 'get'", 'errorRepr': 'AttributeError("\'tuple\' object has no attribute \'get\'")', 'traceback': 'Traceback (most recent call last):\n  File "D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\app\\workflows\\MarketingWorkflow.py", line 556, in executeStreamWorkflow\n    for event in self._handleNodeUpdates(data, threadId, messageId):\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\app\\workflows\\MarketingWorkflow.py", line 967, in _handleNodeUpdates\n    if nodeOutput.get("needCompletion", False):\n       ^^^^^^^^^^^^^^\nAttributeError: \'tuple\' object has no attribute \'get\'\n'}
2025-07-24 18:50:08 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:613 | streamExecutionError: {'errorType': 'TypeError', 'errorMessage': "'async_generator' object is not iterable", 'errorRepr': 'TypeError("\'async_generator\' object is not iterable")', 'traceback': 'Traceback (most recent call last):\n  File "D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\app\\workflows\\MarketingWorkflow.py", line 541, in executeStreamWorkflow\n    if token_event:  # 只有非空内容才发送事件\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "D:\\BaiduNetdiskDownload\\AI\\bi-fang-bird\\app\\workflows\\MarketingWorkflow.py", line 534, in sync_stream\n    # 处理LangGraph原生流式事件\n          ^^^^^^^^^^^^^^^^^^^^^\nTypeError: \'async_generator\' object is not iterable\n'}
2025-07-24 19:25:28 | ERROR    | app.api.routes:generateStreamEvents:341 | 事件序列化失败: Object of type Interrupt is not JSON serializable
2025-07-24 19:25:28 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:597 | streamExecutionError: AttributeError - 'tuple' object has no attribute 'get' - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 546, in executeStreamWorkflow |     for event in self._handleNodeUpdates(data, threadId, messageId): |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 951, in _handleNodeUpdates |     if nodeOutput.get("needCompletion", False): |        ^^^^^^^^^^^^^^ | AttributeError: 'tuple' object has no attribute 'get' | 
2025-07-24 19:32:13 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:597 | streamExecutionError: NotImplementedError - NotImplementedError() - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 529, in executeStreamWorkflow |     async for streamEvent in self.app.astream( |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\__init__.py", line 2596, in astream |     async with AsyncPregelLoop( |                ^^^^^^^^^^^^^^^^ |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\pregel\loop.py", line 1339, in __aenter__ |     saved = await self.checkpointer.aget_tuple(self.checkpoint_config) |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-packages\langgraph\checkpoint\base\__init__.py", line 268, in aget_tuple |     raise NotImplementedError | NotImplementedError | 
2025-07-24 20:49:04 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Unterminated string starting at: line 1 column 141 (char 140)
2025-07-24 20:53:51 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Expecting value: line 1 column 1 (char 0)
2025-07-24 21:07:33 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Invalid \escape: line 1 column 578 (char 577)
2025-07-25 12:19:29 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-19' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 12:46:51 | ERROR    | app.agents.PlanCreatorAgent:generatePlanWithStreaming:233 | 流式方案生成失败: peer closed connection without sending complete message body (incomplete chunked read)
2025-07-25 13:26:50 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:249 | ReAct Agent分析失败: Connection error.
2025-07-25 13:26:52 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:348 | LLM意图提取失败: Connection error.
2025-07-25 13:26:54 | ERROR    | app.agents.IntentionAgent:_generateGuidancePrompts:668 | 全面的引导话术生成失败: Connection error.
2025-07-25 13:26:58 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:249 | ReAct Agent分析失败: Connection error.
2025-07-25 13:27:00 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:348 | LLM意图提取失败: Connection error.
2025-07-25 13:27:01 | ERROR    | app.agents.IntentionAgent:_generateGuidancePrompts:668 | 全面的引导话术生成失败: Connection error.
2025-07-25 13:27:04 | ERROR    | app.agents.RouterAgent:executeSmartRoutingAnalysis:249 | ReAct Agent分析失败: Connection error.
2025-07-25 13:27:06 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:348 | LLM意图提取失败: Connection error.
2025-07-25 13:27:07 | ERROR    | app.agents.IntentionAgent:_generateGuidancePrompts:668 | 全面的引导话术生成失败: Connection error.
2025-07-25 15:50:20 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-13' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 15:53:24 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Unterminated string starting at: line 1 column 145 (char 144)
2025-07-25 15:53:30 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-20' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 15:54:34 | ERROR    | logging:callHandlers:1762 | an error occurred during closing of asynchronous generator <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x000001C6B87F07C0>
asyncgen: <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x000001C6B87F07C0>
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Li...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001C692C5A840>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001C6931D1BE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 687, in run_until_complete
    return future.result()
           │      └ <method 'result' of '_asyncio.Task' objects>
           └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Li...

asyncio.exceptions.CancelledError


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001C6934967A0>
           │      └ <function Runner.run at 0x000001C692C5CCC0>
           └ <asyncio.runners.Runner object at 0x000001C6931D1BE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()

KeyboardInterrupt


During handling of the above exception, another exception occurred:


RuntimeError: async generator ignored GeneratorExit
2025-07-25 16:15:39 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-10' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 16:19:28 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:596 | streamExecutionError: NameError - name 'startTime' is not defined - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 581, in executeStreamWorkflow |     "status": "interrupted", |                     ^^^^^^^^^ | NameError: name 'startTime' is not defined | 
2025-07-25 16:26:51 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-27' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 16:27:47 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-37' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 16:28:49 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-47' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 17:07:30 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:610 | streamExecutionError: NameError - name 'startTime' is not defined - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 595, in executeStreamWorkflow |     elapsedTime = endTime - startTime |                             ^^^^^^^^^ | NameError: name 'startTime' is not defined | 
2025-07-25 17:11:38 | ERROR    | logging:callHandlers:1762 | an error occurred during closing of asynchronous generator <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x0000020F7FC8F1C0>
asyncgen: <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x0000020F7FC8F1C0>
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020F7C784E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020F7C947EF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 687, in run_until_complete
    return future.result()
           │      └ <method 'result' of '_asyncio.Task' objects>
           └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...

asyncio.exceptions.CancelledError


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020F7C925700>
           │      └ <function Runner.run at 0x0000020F7C787240>
           └ <asyncio.runners.Runner object at 0x0000020F7C947EF0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()

KeyboardInterrupt


During handling of the above exception, another exception occurred:


RuntimeError: async generator ignored GeneratorExit
2025-07-25 17:12:51 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-10' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 17:19:56 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:607 | streamExecutionError: NameError - name 'startTime' is not defined - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 592, in executeStreamWorkflow |     elapsedTime = endTime - startTime |                             ^^^^^^^^^ | NameError: name 'startTime' is not defined | 
2025-07-25 17:24:32 | ERROR    | logging:callHandlers:1762 | an error occurred during closing of asynchronous generator <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x000001FCFD832A40>
asyncgen: <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x000001FCFD832A40>
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001FCF9304E00>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001FCF94F7200>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 687, in run_until_complete
    return future.result()
           │      └ <method 'result' of '_asyncio.Task' objects>
           └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...

asyncio.exceptions.CancelledError


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001FCF94D5700>
           │      └ <function Runner.run at 0x000001FCF9307240>
           └ <asyncio.runners.Runner object at 0x000001FCF94F7200>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()

KeyboardInterrupt


During handling of the above exception, another exception occurred:


RuntimeError: async generator ignored GeneratorExit
2025-07-25 17:24:49 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-10' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 17:58:25 | ERROR    | app.workflows.MarketingWorkflow:executeStreamWorkflow:610 | streamExecutionError: NameError - name 'WorkflowStatus' is not defined - Traceback (most recent call last): |   File "D:\BaiduNetdiskDownload\AI\bi-fang-bird\app\workflows\MarketingWorkflow.py", line 600, in executeStreamWorkflow |     status=WorkflowStatus.SUCCEEDED, |            ^^^^^^^^^^^^^^ | NameError: name 'WorkflowStatus' is not defined | 
2025-07-25 18:02:22 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Expecting value: line 1 column 1 (char 0)
2025-07-25 18:02:33 | ERROR    | logging:callHandlers:1762 | an error occurred during closing of asynchronous generator <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x0000023720027C40>
asyncgen: <async_generator object MarketingRoutes.marketingChatStream.<locals>.generateStreamEvents at 0x0000023720027C40>
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000002377FC42480>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000002377FA91880>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 687, in run_until_complete
    return future.result()
           │      └ <method 'result' of '_asyncio.Task' objects>
           └ <Task cancelled name='Task-1' coro=<Server.serve() done, defined at D:\BaiduNetdiskDownload\AI\bi-fang-bird\venv\Lib\site-pac...

asyncio.exceptions.CancelledError


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000002377FCD5C40>
           │      └ <function Runner.run at 0x000002377FC3C900>
           └ <asyncio.runners.Runner object at 0x000002377FA91880>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 123, in run
    raise KeyboardInterrupt()

KeyboardInterrupt


During handling of the above exception, another exception occurred:


RuntimeError: async generator ignored GeneratorExit
2025-07-25 18:03:09 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-10' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-25 18:05:09 | ERROR    | logging:callHandlers:1762 | Task exception was never retrieved
future: <Task finished name='Task-20' coro=<<async_generator_athrow without __name__>()> exception=RuntimeError('async generator ignored GeneratorExit')>
RuntimeError: async generator ignored GeneratorExit
2025-07-29 20:52:10 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:449 | LLM输出无法解析为JSON: Expecting value: line 1 column 1 (char 0)
2025-07-29 20:52:18 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:449 | LLM输出无法解析为JSON: Expecting value: line 1 column 1 (char 0)
2025-07-29 20:52:25 | ERROR    | app.agents.IntentionAgent:_extractIntentWithRetry:449 | LLM输出无法解析为JSON: Expecting value: line 1 column 1 (char 0)
2025-07-29 20:52:25 | ERROR    | app.agents.IntentionAgent:_validateAndStandardizeIntent:574 | 意图数据标准化失败: 'NoneType' object has no attribute 'get'
2025-07-29 20:58:28 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Invalid \escape: line 1 column 214 (char 213)
2025-07-29 20:58:54 | ERROR    | app.agents.tools.RouterAgentTools:routerTool:534 | 路由决策失败: Invalid \escape: line 1 column 213 (char 212)
