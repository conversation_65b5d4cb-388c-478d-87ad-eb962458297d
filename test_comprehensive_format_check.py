#!/usr/bin/env python3
"""
全面测试格式过滤问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_comprehensive_format_filtering():
    """全面测试格式过滤问题"""
    
    workflow = MarketingWorkflow()
    
    print("=== 全面测试格式过滤问题 ===\n")
    
    # 测试完整的thinking内容处理流程
    full_llm_output = """<thinking>
## 我来帮您梳理需求

听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，尤其是针对大学生群体。

大学生在暑假期间通常会有较多的闲暇时间和消费需求，比如：
- 购买电子产品
- 旅游出行
- 娱乐消费

这为银行提供了很好的交叉销售机会。

## 当前掌握的信息

从您的描述中，我可以提取到以下核心要素：

1. **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用
2. **预算**：2万元，这个金额可以支持一些线上推广活动
3. **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群

## 还需要补充的信息

尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：

- **活动名称**：活动需要一个吸引人的名称
- **活动时间**：暑期的具体时间段
- **活动内容**：具体的促销形式是什么？
</thinking>

{
  "goal": "促进大学生金融产品使用",
  "budget": "2万元",
  "audience": "大学生群体"
}"""
    
    print("1. 测试thinking内容过滤")
    print("=" * 50)
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 分块处理（模拟流式输入）
    chunks = [
        "<thinking>",
        "\n## 我来帮您梳理需求\n\n",
        "听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，",
        "尤其是针对大学生群体。\n\n",
        "大学生在暑假期间通常会有较多的闲暇时间和消费需求，比如：\n",
        "- 购买电子产品\n",
        "- 旅游出行\n", 
        "- 娱乐消费\n\n",
        "这为银行提供了很好的交叉销售机会。\n\n",
        "## 当前掌握的信息\n\n",
        "从您的描述中，我可以提取到以下核心要素：\n\n",
        "1. **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用\n",
        "2. **预算**：2万元，这个金额可以支持一些线上推广活动\n",
        "3. **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群\n\n",
        "## 还需要补充的信息\n\n",
        "尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：\n\n",
        "- **活动名称**：活动需要一个吸引人的名称\n",
        "- **活动时间**：暑期的具体时间段\n",
        "- **活动内容**：具体的促销形式是什么？\n",
        "</thinking>",
        "\n\n{",
        '\n  "goal": "促进大学生金融产品使用",',
        '\n  "budget": "2万元"',
        "\n}"
    ]
    
    accumulated_content = ""
    
    for i, chunk in enumerate(chunks):
        result = workflow._filterThinkingContent(chunk)
        if result:
            accumulated_content += result
            print(f"块{i+1:2d}: 输出 {len(result):3d} 字符")
        else:
            print(f"块{i+1:2d}: 被过滤")
    
    print(f"\n最终累积内容长度: {len(accumulated_content)}")
    print(f"包含换行符: {'\\n' in accumulated_content}")
    print(f"包含Markdown标题: {'##' in accumulated_content}")
    print(f"包含Markdown粗体: {'**' in accumulated_content}")
    print(f"包含列表: {'-' in accumulated_content}")
    
    print("\n2. 测试其他可能的格式过滤方法")
    print("=" * 50)
    
    # 测试_isToolCallContent方法
    test_content = accumulated_content[:200] + "..."
    is_tool_call = workflow._isToolCallContent(test_content)
    print(f"_isToolCallContent: {is_tool_call}")
    
    # 测试自然语言检查
    import re
    has_natural_language = bool(re.search(r'[a-zA-Z\u4e00-\u9fff]', accumulated_content))
    print(f"包含自然语言: {has_natural_language}")
    
    # 测试_isBusinessContent方法
    is_business_content = workflow._isBusinessContent(accumulated_content)
    print(f"_isBusinessContent: {is_business_content}")
    
    # 测试_formatBusinessContent方法
    mock_token_event = {
        'content': accumulated_content[:100],  # 取前100字符测试
        'data': {
            'nodeId': 'INTENTION_AGENT',
            'contentType': 'businessInsight'
        }
    }
    
    formatted_event = workflow._formatBusinessContent(mock_token_event)
    if formatted_event:
        original_content = mock_token_event['content']
        formatted_content = formatted_event['content']
        content_changed = original_content != formatted_content
        print(f"_formatBusinessContent 是否修改内容: {content_changed}")
        if content_changed:
            print(f"  原始: {original_content[:50]}...")
            print(f"  格式化后: {formatted_content[:50]}...")
    
    print("\n3. 测试完整的_handleLLMToken流程")
    print("=" * 50)
    
    # 模拟AIMessageChunk
    class MockAIMessageChunk:
        def __init__(self, content, chunk_id="test_chunk_001"):
            self.content = content
            self.id = chunk_id
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试几个关键的chunk
    test_chunks = [
        "<thinking>",
        "\n## 我来帮您梳理需求\n\n",
        "听到您提到**暑期促销活动**，这是一个很好的机会。\n\n",
        "</thinking>"
    ]
    
    print("完整流程测试:")
    for i, chunk_content in enumerate(test_chunks):
        mock_chunk = MockAIMessageChunk(chunk_content)
        mock_message_data = (mock_chunk, {})
        
        try:
            result = workflow._handleLLMToken(mock_message_data, "test_thread", "test_message")
            if result:
                content = result.get('content', '')
                print(f"  块{i+1}: 输出 '{content[:30]}...' (长度: {len(content)})")
            else:
                print(f"  块{i+1}: 返回None")
        except Exception as e:
            print(f"  块{i+1}: 异常 - {e}")

def test_format_preservation():
    """测试格式保留的详细情况"""
    
    workflow = MarketingWorkflow()
    
    print("\n4. 测试格式保留详细情况")
    print("=" * 50)
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试各种格式元素
    format_tests = [
        ("标题", "## 我来分析一下"),
        ("粗体", "**重要信息**"),
        ("列表", "- 第一项\n- 第二项"),
        ("编号列表", "1. 第一点\n2. 第二点"),
        ("换行", "第一行\n\n第二行"),
        ("混合格式", "## 标题\n\n**粗体**内容\n\n- 列表项")
    ]
    
    for test_name, test_content in format_tests:
        # 包装在thinking标签中
        wrapped_content = f"<thinking>{test_content}</thinking>"
        
        # 重置状态
        workflow._resetThinkingState()
        
        # 分块处理
        chunks = ["<thinking>", test_content, "</thinking>"]
        result_content = ""
        
        for chunk in chunks:
            result = workflow._filterThinkingContent(chunk)
            if result:
                result_content += result
        
        # 检查格式是否保留
        preserved = test_content in result_content or result_content == test_content
        print(f"  {test_name}: {'✅' if preserved else '❌'}")
        if not preserved:
            print(f"    原始: '{test_content}'")
            print(f"    结果: '{result_content}'")

if __name__ == "__main__":
    print("开始全面测试格式过滤问题...\n")
    
    try:
        test_comprehensive_format_filtering()
        test_format_preservation()
        print("\n🎉 全面测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
