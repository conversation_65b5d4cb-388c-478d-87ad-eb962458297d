#!/usr/bin/env python3
"""
测试通用的思考过程过滤功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_universal_thinking_filter():
    """测试通用的思考过程过滤功能"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试通用思考过程过滤功能 ===\n")
    
    # 测试用例1: thinking标签内容应该被提取
    thinking_content = """
    <thinking>
    ## 我来分析一下您的需求
    
    看到您想做一个**提升信用卡申请转化率**的活动，这个目标很明确！
    
    ## 目前掌握的信息
    
    从您的描述中，我能明确提取到的是活动的核心目标。
    </thinking>
    """
    
    result1 = workflow._filterAgentThinkingContent(thinking_content, "INTENTION_AGENT")
    print(f"测试1 - thinking标签提取:")
    print(f"输入: {thinking_content[:50]}...")
    print(f"输出: {result1[:100]}...")
    print(f"是否提取成功: {len(result1) > 0 and '我来分析一下您的需求' in result1}\n")
    
    # 测试用例2: JSON内容应该被过滤
    json_content = '''
    {
      "goal": "提升信用卡申请转化率",
      "budget": "10万元",
      "audience": "25-40岁上班族"
    }
    '''
    
    result2 = workflow._filterAgentThinkingContent(json_content, "INTENTION_AGENT")
    print(f"测试2 - JSON内容过滤:")
    print(f"输入: {json_content[:50]}...")
    print(f"输出: '{result2}'")
    print(f"是否过滤成功: {result2 == ''}\n")
    
    # 测试用例3: 自然语言思考内容应该被保留
    natural_thinking = "我认为这个营销活动需要考虑几个关键因素：目标客群的特征、预算分配策略，以及渠道选择。"
    
    result3 = workflow._filterAgentThinkingContent(natural_thinking, "PLAN_CREATOR_AGENT")
    print(f"测试3 - 自然语言思考保留:")
    print(f"输入: {natural_thinking}")
    print(f"输出: {result3}")
    print(f"是否保留成功: {result3 == natural_thinking}\n")
    
    # 测试用例4: 技术性内容应该被过滤
    technical_content = "nextNode: PLAN_CREATOR_AGENT, threadId: abc123, messageId: msg456"
    
    result4 = workflow._filterAgentThinkingContent(technical_content, "ROUTER_AGENT")
    print(f"测试4 - 技术性内容过滤:")
    print(f"输入: {technical_content}")
    print(f"输出: '{result4}'")
    print(f"是否过滤成功: {result4 == ''}\n")
    
    # 测试用例5: Markdown格式的思考内容应该被保留
    markdown_thinking = """
    ## 营销策略分析
    
    基于当前的市场环境，我建议采用以下策略：
    
    1. **目标客群定位** - 重点关注25-40岁的上班族
    2. **渠道选择** - 优先考虑数字化渠道
    3. **激励设计** - 采用实用性强的奖励
    """
    
    result5 = workflow._filterAgentThinkingContent(markdown_thinking, "PLAN_CREATOR_AGENT")
    print(f"测试5 - Markdown思考内容保留:")
    print(f"输入: {markdown_thinking[:80]}...")
    print(f"输出: {result5[:80]}...")
    print(f"是否保留成功: {len(result5) > 0 and '营销策略分析' in result5}\n")
    
    # 测试用例6: 空内容和短内容应该被过滤
    empty_content = ""
    short_content = "ok"
    
    result6a = workflow._filterAgentThinkingContent(empty_content, "INTENTION_AGENT")
    result6b = workflow._filterAgentThinkingContent(short_content, "INTENTION_AGENT")
    print(f"测试6 - 空内容和短内容过滤:")
    print(f"空内容输出: '{result6a}'")
    print(f"短内容输出: '{result6b}'")
    print(f"是否过滤成功: {result6a == '' and result6b == ''}\n")

def test_thinking_extraction():
    """测试thinking标签内容提取功能"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试thinking标签内容提取 ===\n")
    
    # 测试用例1: 完整的thinking标签
    complete_thinking = """
    <thinking>
    ## 分析用户需求
    
    用户想要制定一个营销活动，目标是提升转化率。
    
    ## 关键考虑因素
    
    1. 预算限制
    2. 目标客群
    3. 渠道选择
    </thinking>
    
    其他内容不应该被包含
    """
    
    result1 = workflow._extractThinkingContent(complete_thinking)
    print(f"测试1 - 完整thinking标签:")
    print(f"提取结果: {result1[:100]}...")
    print(f"是否正确: {'分析用户需求' in result1 and '其他内容' not in result1}\n")
    
    # 测试用例2: 只有开始标签
    partial_thinking = """
    <thinking>
    这是一个部分的thinking内容，没有结束标签。
    我们需要分析营销策略。
    """
    
    result2 = workflow._extractThinkingContent(partial_thinking)
    print(f"测试2 - 部分thinking标签:")
    print(f"提取结果: {result2[:80]}...")
    print(f"是否正确: {'部分的thinking内容' in result2}\n")
    
    # 测试用例3: 没有thinking标签
    no_thinking = "这里没有thinking标签，只是普通内容。"
    
    result3 = workflow._extractThinkingContent(no_thinking)
    print(f"测试3 - 无thinking标签:")
    print(f"提取结果: '{result3}'")
    print(f"是否正确: {result3 == ''}\n")

def test_content_classification():
    """测试内容分类功能"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试内容分类功能 ===\n")
    
    # 测试技术性内容检测
    technical_samples = [
        "nextNode: PLAN_CREATOR_AGENT",
        "threadId: abc123",
        "function_call: analyze_intent",
        "tool_use: customer_analysis",
        "workflow_status: running"
    ]
    
    print("技术性内容检测:")
    for sample in technical_samples:
        is_technical = workflow._isTechnicalContent(sample)
        print(f"  '{sample}' -> {is_technical}")
    
    # 测试自然语言思考内容检测
    thinking_samples = [
        "我认为这个方案需要进一步分析客户需求。",
        "基于当前的市场环境，我建议采用以下策略。",
        "## 营销策略分析\n\n这是一个重要的考虑因素。",
        "考虑到预算限制，我们需要优化渠道选择。",
        "abc123",  # 应该被识别为非思考内容
        "{}"  # 应该被识别为非思考内容
    ]
    
    print("\n自然语言思考内容检测:")
    for sample in thinking_samples:
        is_thinking = workflow._isNaturalLanguageThinking(sample)
        print(f"  '{sample[:50]}...' -> {is_thinking}")

if __name__ == "__main__":
    print("开始测试通用思考过程过滤功能...\n")
    
    try:
        test_universal_thinking_filter()
        test_thinking_extraction()
        test_content_classification()
        print("\n🎉 所有测试完成！通用思考过程过滤功能实现正确。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
