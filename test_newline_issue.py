#!/usr/bin/env python3
"""
专门测试换行符丢失问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_newline_preservation():
    """测试换行符保留问题"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试换行符保留问题 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试包含换行符的内容
    test_chunks = [
        "<thinking>",
        "第一行\n",
        "第二行\n\n",
        "第三行",
        "</thinking>"
    ]
    
    print("逐块处理测试:")
    accumulated = ""
    
    for i, chunk in enumerate(test_chunks):
        print(f"\n块{i+1}: '{repr(chunk)}'")
        result = workflow._filterThinkingContent(chunk)
        if result:
            print(f"  输出: '{repr(result)}'")
            accumulated += result
        else:
            print(f"  输出: 被过滤")
    
    print(f"\n最终累积结果: '{repr(accumulated)}'")
    print(f"包含换行符: {'\\n' in accumulated}")
    print(f"换行符数量: {accumulated.count(chr(10))}")  # chr(10) 是 \n
    
    # 测试单独的换行符处理
    print("\n单独换行符测试:")
    workflow._resetThinkingState()
    
    newline_chunks = [
        "<thinking>",
        "\n",
        "内容",
        "\n\n", 
        "</thinking>"
    ]
    
    newline_accumulated = ""
    for i, chunk in enumerate(newline_chunks):
        print(f"块{i+1}: '{repr(chunk)}'")
        result = workflow._filterThinkingContent(chunk)
        if result:
            print(f"  输出: '{repr(result)}'")
            newline_accumulated += result
        else:
            print(f"  输出: 被过滤")
    
    print(f"\n换行符测试结果: '{repr(newline_accumulated)}'")

def test_thinking_buffer_behavior():
    """测试thinking缓冲区行为"""
    
    workflow = MarketingWorkflow()
    
    print("\n=== 测试thinking缓冲区行为 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 检查初始状态
    print("初始状态:")
    print(f"  _thinking_buffer: '{getattr(workflow, '_thinking_buffer', 'NOT_SET')}'")
    print(f"  _in_thinking: {getattr(workflow, '_in_thinking', 'NOT_SET')}")
    
    # 逐步处理并检查状态
    test_sequence = [
        "<thinking>",
        "内容1\n",
        "内容2\n\n",
        "内容3",
        "</thinking>"
    ]
    
    for i, chunk in enumerate(test_sequence):
        print(f"\n处理块{i+1}: '{repr(chunk)}'")
        result = workflow._filterThinkingContent(chunk)
        
        print(f"  输出: '{repr(result) if result else 'None'}'")
        print(f"  _thinking_buffer: '{repr(getattr(workflow, '_thinking_buffer', 'NOT_SET'))}'")
        print(f"  _in_thinking: {getattr(workflow, '_in_thinking', 'NOT_SET')}")

def test_direct_newline_handling():
    """直接测试换行符处理"""
    
    workflow = MarketingWorkflow()
    
    print("\n=== 直接测试换行符处理 ===\n")
    
    # 测试不同类型的换行符
    newline_tests = [
        ("单个\\n", "\n"),
        ("双个\\n\\n", "\n\n"),
        ("文本+\\n", "文本\n"),
        ("\\n+文本", "\n文本"),
        ("文本+\\n+文本", "文本\n文本"),
        ("复杂换行", "第一行\n\n第二行\n第三行")
    ]
    
    for test_name, test_content in newline_tests:
        # 包装在thinking标签中
        workflow._resetThinkingState()
        
        # 分三步：开始标签、内容、结束标签
        chunks = ["<thinking>", test_content, "</thinking>"]
        result_content = ""
        
        for chunk in chunks:
            result = workflow._filterThinkingContent(chunk)
            if result:
                result_content += result
        
        print(f"{test_name}:")
        print(f"  输入: '{repr(test_content)}'")
        print(f"  输出: '{repr(result_content)}'")
        print(f"  保留: {test_content == result_content}")

if __name__ == "__main__":
    print("开始测试换行符丢失问题...\n")
    
    try:
        test_newline_preservation()
        test_thinking_buffer_behavior()
        test_direct_newline_handling()
        print("\n🎉 换行符测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
