#!/usr/bin/env python3
"""
测试thinking修复效果
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_thinking_fix():
    """测试thinking修复效果"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试thinking修复效果 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟真实的LLM流式输出chunks
    chunks = [
        "<thinking>",
        "\n## 我来分析一下您的需求\n\n",
        "听您说想做一个**暑期促销活动**，这个主题非常契合季节特点。\n\n",
        "## 当前掌握的信息\n\n",
        "从您的描述中，我可以提取到以下核心要素：\n\n",
        "- **活动目标**：提升品牌知名度\n",
        "- **预算**：2万元\n",
        "- **目标客群**：大学生群体\n\n",
        "</thinking>",
        "\n\n{",
        '\n  "goal": "提升品牌知名度"',
        "\n}"
    ]
    
    print("模拟流式处理:")
    print("=" * 60)
    
    accumulated_thinking = ""
    
    for i, chunk in enumerate(chunks):
        print(f"\n处理chunk {i+1}: '{chunk[:30]}...'")
        
        result = workflow._filterThinkingContent(chunk)
        
        if result:
            accumulated_thinking += result
            print(f"  输出: '{result[:50]}...' (长度: {len(result)})")
            
            # 检查是否保留了格式
            has_newlines = '\n' in result
            has_markdown = '##' in result or '**' in result
            print(f"  包含换行符: {has_newlines}")
            print(f"  包含Markdown: {has_markdown}")
        else:
            print(f"  输出: 被过滤")
    
    print(f"\n最终结果分析:")
    print("=" * 60)
    print(f"累积thinking内容长度: {len(accumulated_thinking)}")
    print(f"包含换行符: {'\\n' in accumulated_thinking}")
    print(f"换行符数量: {accumulated_thinking.count(chr(10))}")
    print(f"包含Markdown标题: {'##' in accumulated_thinking}")
    print(f"包含Markdown粗体: {'**' in accumulated_thinking}")
    print(f"包含列表项: {'- **' in accumulated_thinking}")
    
    print(f"\n前端应该接收到的完整内容:")
    print("=" * 40)
    print(accumulated_thinking)
    print("=" * 40)
    
    # 验证内容质量
    expected_elements = [
        "## 我来分析一下您的需求",
        "**暑期促销活动**",
        "## 当前掌握的信息", 
        "- **活动目标**",
        "- **预算**",
        "- **目标客群**"
    ]
    
    found_elements = [elem for elem in expected_elements if elem in accumulated_thinking]
    
    print(f"\n内容质量检查:")
    print(f"期望元素: {len(expected_elements)}")
    print(f"找到元素: {len(found_elements)}")
    print(f"完整性: {len(found_elements) / len(expected_elements) * 100:.1f}%")
    
    if len(found_elements) == len(expected_elements):
        print("✅ thinking内容完整，格式正确！")
    else:
        print("❌ thinking内容不完整")
        print(f"缺失元素: {set(expected_elements) - set(found_elements)}")

def test_edge_cases():
    """测试边界情况"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 测试边界情况 ===\n")
    
    # 测试用例1: 空的thinking标签
    print("测试1: 空的thinking标签")
    workflow._resetThinkingState()
    
    empty_chunks = ["<thinking>", "</thinking>"]
    result1 = ""
    for chunk in empty_chunks:
        r = workflow._filterThinkingContent(chunk)
        if r:
            result1 += r
    
    print(f"  结果: '{result1}'")
    print(f"  正确: {result1 == ''}")
    
    # 测试用例2: 只有开始标签
    print("\n测试2: 只有开始标签")
    workflow._resetThinkingState()
    
    partial_chunks = ["<thinking>", "内容1", "内容2"]
    result2 = ""
    for chunk in partial_chunks:
        r = workflow._filterThinkingContent(chunk)
        if r:
            result2 += r
    
    print(f"  结果: '{result2}'")
    print(f"  正确: {'内容1内容2' in result2}")
    
    # 测试用例3: 混合内容
    print("\n测试3: 混合内容")
    workflow._resetThinkingState()
    
    mixed_chunks = [
        "前置内容",
        "<thinking>",
        "thinking内容",
        "</thinking>",
        "后置内容"
    ]
    
    result3 = ""
    for chunk in mixed_chunks:
        r = workflow._filterThinkingContent(chunk)
        if r:
            result3 += r
    
    print(f"  结果: '{result3}'")
    print(f"  正确: {result3 == 'thinking内容'}")

if __name__ == "__main__":
    print("开始测试thinking修复效果...\n")
    
    try:
        test_thinking_fix()
        test_edge_cases()
        print("\n🎉 thinking修复测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
