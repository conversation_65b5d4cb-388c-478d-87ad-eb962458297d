#!/usr/bin/env python3
"""
测试JSON转义修复
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.tools.RouterAgentTools import _safe_json_dumps

def test_json_escape_fix():
    """测试JSON转义修复功能"""
    
    print("=== 测试JSON转义修复功能 ===\n")
    
    # 测试用例1: 包含反斜杠的字符串
    test_data1 = {
        "nextNode": "INTENTION_AGENT",
        "reasoning": "路径是C:\\Users\\<USER>\\file.txt，需要处理",
        "confidence": 0.8
    }
    
    try:
        result1 = _safe_json_dumps(test_data1)
        parsed1 = json.loads(result1)  # 验证可以正确解析
        print("测试1 - 反斜杠转义:")
        print(f"✅ 成功处理反斜杠")
        print(f"   原始: {test_data1['reasoning']}")
        print(f"   结果: {parsed1['reasoning']}")
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
    
    # 测试用例2: 包含双引号的字符串
    test_data2 = {
        "nextNode": "PLAN_CREATOR_AGENT",
        "reasoning": '用户说"我想要一个好方案"，需要详细分析',
        "confidence": 0.9
    }
    
    try:
        result2 = _safe_json_dumps(test_data2)
        parsed2 = json.loads(result2)
        print("\n测试2 - 双引号转义:")
        print(f"✅ 成功处理双引号")
        print(f"   原始: {test_data2['reasoning']}")
        print(f"   结果: {parsed2['reasoning']}")
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    
    # 测试用例3: 包含换行符的字符串
    test_data3 = {
        "nextNode": "TAG_CUSTOMER_AGENT",
        "reasoning": "用户输入包含多行：\n第一行\n第二行\n需要处理",
        "confidence": 0.7
    }
    
    try:
        result3 = _safe_json_dumps(test_data3)
        parsed3 = json.loads(result3)
        print("\n测试3 - 换行符转义:")
        print(f"✅ 成功处理换行符")
        print(f"   原始长度: {len(test_data3['reasoning'])}")
        print(f"   结果长度: {len(parsed3['reasoning'])}")
    except Exception as e:
        print(f"❌ 测试3失败: {e}")
    
    # 测试用例4: 正常数据（应该不受影响）
    test_data4 = {
        "nextNode": "INTENTION_AGENT",
        "reasoning": "正常的推理过程，没有特殊字符",
        "confidence": 0.8,
        "strategy": "normal"
    }
    
    try:
        result4 = _safe_json_dumps(test_data4)
        parsed4 = json.loads(result4)
        print("\n测试4 - 正常数据:")
        print(f"✅ 正常数据处理正确")
        print(f"   nextNode: {parsed4['nextNode']}")
        print(f"   strategy: {parsed4['strategy']}")
    except Exception as e:
        print(f"❌ 测试4失败: {e}")
    
    # 测试用例5: 复杂嵌套数据
    test_data5 = {
        "nextNode": "PLAN_CREATOR_AGENT",
        "reasoning": "复杂情况：路径C:\\test\\，引号\"内容\"，换行\n符",
        "metadata": {
            "path": "C:\\Users\\<USER>\"hello\"",
            "lines": ["第一行\n", "第二行\t制表符"]
        },
        "confidence": 0.6
    }
    
    try:
        result5 = _safe_json_dumps(test_data5)
        parsed5 = json.loads(result5)
        print("\n测试5 - 复杂嵌套数据:")
        print(f"✅ 复杂数据处理正确")
        print(f"   reasoning: {parsed5['reasoning'][:50]}...")
        print(f"   metadata.path: {parsed5['metadata']['path']}")
    except Exception as e:
        print(f"❌ 测试5失败: {e}")

if __name__ == "__main__":
    print("开始测试JSON转义修复功能...\n")
    
    try:
        test_json_escape_fix()
        print("\n🎉 JSON转义修复功能测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
