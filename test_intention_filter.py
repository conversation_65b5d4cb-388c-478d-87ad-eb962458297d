#!/usr/bin/env python3
"""
测试意图识别Agent内容过滤功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_intention_agent_filter():
    """测试意图识别Agent的内容过滤功能"""
    
    workflow = MarketingWorkflow()
    
    # 测试用例1: 应该被过滤的JSON内容
    json_content = '''
    {
      "goal": "提升信用卡申请转化率",
      "budget": "",
      "audience": "",
      "missingCoreFields": ["budget", "audience"]
    }
    '''
    
    result1 = workflow._filterIntentionAgentContent(json_content)
    print(f"测试1 - JSON内容过滤: '{json_content[:50]}...' -> '{result1}'")
    assert result1 == "", "JSON内容应该被过滤"
    
    # 测试用例2: 应该被过滤的JSON标记
    json_marker_content = "```json"
    result2 = workflow._filterIntentionAgentContent(json_marker_content)
    print(f"测试2 - JSON标记过滤: '{json_marker_content}' -> '{result2}'")
    assert result2 == "", "JSON标记应该被过滤"
    
    # 测试用例3: 应该被过滤的结果分隔符
    result_marker = "---RESULT_START---"
    result3 = workflow._filterIntentionAgentContent(result_marker)
    print(f"测试3 - 结果分隔符过滤: '{result_marker}' -> '{result3}'")
    assert result3 == "", "结果分隔符应该被过滤"
    
    # 测试用例4: 应该保留的自然语言内容
    natural_content = "## 我来分析一下您的需求\n\n看到您想做一个**提升信用卡申请转化率**的活动，这个目标很明确！"
    result4 = workflow._filterIntentionAgentContent(natural_content)
    print(f"测试4 - 自然语言保留: '{natural_content[:30]}...' -> '{result4[:30]}...'")
    assert result4 == natural_content, "自然语言内容应该被保留"
    
    # 测试用例5: 应该保留的Markdown内容
    markdown_content = "**预算问题** - 这直接决定了我们能做多大规模的活动"
    result5 = workflow._filterIntentionAgentContent(markdown_content)
    print(f"测试5 - Markdown保留: '{markdown_content}' -> '{result5}'")
    assert result5 == markdown_content, "Markdown内容应该被保留"
    
    # 测试用例6: 应该被过滤的JSON字段
    json_field_content = '"goal": "提升转化率",'
    result6 = workflow._filterIntentionAgentContent(json_field_content)
    print(f"测试6 - JSON字段过滤: '{json_field_content}' -> '{result6}'")
    assert result6 == "", "JSON字段应该被过滤"
    
    print("\n✅ 所有测试通过！意图识别Agent内容过滤功能正常工作。")

def test_intention_json_detection():
    """测试意图识别Agent的JSON内容检测功能"""
    
    workflow = MarketingWorkflow()
    
    # 测试用例1: 包含意图字段的内容
    content1 = '"goal": "提升转化率", "audience": "年轻用户"'
    result1 = workflow._isIntentionAgentJsonContent(content1)
    print(f"JSON检测1: '{content1}' -> {result1}")
    assert result1 == True, "应该检测为JSON内容"
    
    # 测试用例2: 完整的JSON对象
    content2 = '{"goal": "提升转化率", "budget": "10万元"}'
    result2 = workflow._isIntentionAgentJsonContent(content2)
    print(f"JSON检测2: '{content2}' -> {result2}")
    assert result2 == True, "应该检测为JSON内容"
    
    # 测试用例3: 自然语言内容
    content3 = "这是一个营销活动的分析过程，我们需要考虑目标客群。"
    result3 = workflow._isIntentionAgentJsonContent(content3)
    print(f"JSON检测3: '{content3}' -> {result3}")
    assert result3 == False, "不应该检测为JSON内容"
    
    # 测试用例4: Markdown内容
    content4 = "## 我的专业建议\n\n基于多年银行营销经验..."
    result4 = workflow._isIntentionAgentJsonContent(content4)
    print(f"JSON检测4: '{content4[:30]}...' -> {result4}")
    assert result4 == False, "不应该检测为JSON内容"
    
    print("\n✅ JSON检测功能测试通过！")

if __name__ == "__main__":
    print("开始测试意图识别Agent内容过滤功能...\n")
    
    try:
        test_intention_agent_filter()
        test_intention_json_detection()
        print("\n🎉 所有测试完成！功能实现正确。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
