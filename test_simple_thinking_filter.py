#!/usr/bin/env python3
"""
测试简化的thinking内容过滤功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_simple_thinking_filter():
    """测试简化的thinking内容过滤功能"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试简化的thinking内容过滤功能 ===\n")
    
    # 模拟流式输入序列
    stream_inputs = [
        "<th",           # 部分开始标签
        "inking>",       # 完成开始标签
        "## 我来分析",    # thinking内容1
        "一下您的需求\n\n", # thinking内容2
        "看到您想做一个", # thinking内容3
        "**提升转化率**", # thinking内容4
        "的活动</th",    # thinking内容5 + 部分结束标签
        "inking>",       # 完成结束标签
        "\n\n{",         # JSON开始（应该被过滤）
        '"goal": "提升转化率"', # JSON内容（应该被过滤）
        "}"              # JSON结束（应该被过滤）
    ]
    
    print("模拟流式输入处理:")
    results = []
    for i, input_chunk in enumerate(stream_inputs):
        result = workflow._filterThinkingContent(input_chunk)
        results.append(result)
        print(f"输入{i+1}: '{input_chunk}' -> 输出: '{result}'")
    
    # 验证结果
    print(f"\n验证结果:")
    
    # 前两个输入应该被过滤（标签本身）
    print(f"1. 部分标签被过滤: {results[0] == '' and results[1] == ''}")
    
    # thinking内容应该被保留
    thinking_content = "".join(results[2:7])  # 索引2-6是thinking内容
    expected_thinking = "## 我来分析一下您的需求\n\n看到您想做一个**提升转化率**的活动"
    print(f"2. thinking内容正确提取: {thinking_content == expected_thinking}")
    print(f"   提取的内容: '{thinking_content}'")
    
    # 结束标签和JSON内容应该被过滤
    json_filtered = all(result == '' for result in results[7:])
    print(f"3. JSON内容被过滤: {json_filtered}")

def test_multiple_thinking_blocks():
    """测试多个thinking块的处理"""
    
    workflow = MarketingWorkflow()
    
    print("\n=== 测试多个thinking块 ===\n")
    
    # 重置状态
    workflow._resetThinkingState()
    
    # 第一个thinking块
    inputs1 = [
        "<thinking>",
        "第一个思考过程",
        "</thinking>",
        "其他内容应该被过滤"
    ]
    
    print("第一个thinking块:")
    for i, input_chunk in enumerate(inputs1):
        result = workflow._filterThinkingContent(input_chunk)
        print(f"  输入: '{input_chunk}' -> 输出: '{result}'")
    
    # 重置状态，模拟新的LLM调用
    workflow._resetThinkingState()
    
    # 第二个thinking块
    inputs2 = [
        "前面的内容",
        "<thinking>",
        "第二个思考过程",
        "</thinking>"
    ]
    
    print("\n第二个thinking块:")
    for i, input_chunk in enumerate(inputs2):
        result = workflow._filterThinkingContent(input_chunk)
        print(f"  输入: '{input_chunk}' -> 输出: '{result}'")

def test_edge_cases():
    """测试边界情况"""
    
    workflow = MarketingWorkflow()
    
    print("\n=== 测试边界情况 ===\n")
    
    # 重置状态
    workflow._resetThinkingState()
    
    edge_cases = [
        "",                    # 空字符串
        "<thinking>",          # 只有开始标签
        "</thinking>",         # 只有结束标签（不在thinking状态）
        "<thinking></thinking>", # 空的thinking块
        "普通文本",             # 普通文本（不在thinking状态）
    ]
    
    print("边界情况测试:")
    for i, input_chunk in enumerate(edge_cases):
        result = workflow._filterThinkingContent(input_chunk)
        print(f"  输入: '{input_chunk}' -> 输出: '{result}'")

if __name__ == "__main__":
    print("开始测试简化的thinking内容过滤功能...\n")
    
    try:
        test_simple_thinking_filter()
        test_multiple_thinking_blocks()
        test_edge_cases()
        print("\n🎉 所有测试完成！简化的thinking过滤功能工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
