#!/usr/bin/env python3
"""
直接测试流式接口
"""

import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

async def test_real_stream_api():
    """直接测试流式接口"""
    
    print("=== 直接测试流式接口 ===\n")
    
    workflow = MarketingWorkflow()
    
    # 模拟真实的用户输入
    user_input = "我想做一个暑期促销活动，目标客群是大学生群体，预算2万元"
    
    print(f"用户输入: {user_input}")
    print("=" * 60)
    
    # 收集所有流式事件
    stream_events = []
    accumulated_content = ""
    
    try:
        # 调用真实的流式接口
        async for event in workflow.executeStreamWorkflow(
            userInput=user_input,
            threadId="test_thread_001",
            messageId="test_msg_001"
        ):
            # 收集事件
            stream_events.append(event)
            
            # 打印事件信息
            event_type = event.get('event', 'unknown')
            
            if event_type == 'message':
                content = event.get('content', '')
                content_type = event.get('data', {}).get('contentType', 'unknown')
                node_id = event.get('data', {}).get('nodeId', 'unknown')
                
                print(f"📨 MESSAGE事件:")
                print(f"   contentType: {content_type}")
                print(f"   nodeId: {node_id}")
                print(f"   content长度: {len(content)}")
                print(f"   content: '{content[:100]}...' " if len(content) > 100 else f"   content: '{content}'")
                
                # 检查是否是thinking内容
                if content and any(marker in content for marker in ['##', '**', '我来', '分析', '建议']):
                    accumulated_content += content
                    print(f"   ✅ 疑似thinking内容")
                else:
                    print(f"   ❓ 其他内容")
                
            elif event_type == 'business_progress':
                stage = event.get('stage', 'unknown')
                progress = event.get('progress', 0)
                print(f"📊 PROGRESS事件: {stage} ({progress}%)")
                
            elif event_type == 'workflow_complete':
                print(f"✅ WORKFLOW_COMPLETE事件")
                
            else:
                print(f"🔍 其他事件: {event_type}")
            
            print()
    
    except Exception as e:
        print(f"❌ 流式接口调用失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("=" * 60)
    print("📋 流式事件分析")
    print("=" * 60)
    
    # 统计事件类型
    event_types = {}
    message_events = []
    
    for event in stream_events:
        event_type = event.get('event', 'unknown')
        event_types[event_type] = event_types.get(event_type, 0) + 1
        
        if event_type == 'message':
            message_events.append(event)
    
    print(f"总事件数量: {len(stream_events)}")
    print(f"事件类型统计: {event_types}")
    print(f"MESSAGE事件数量: {len(message_events)}")
    
    if message_events:
        print(f"\n📨 MESSAGE事件详细分析:")
        
        # 按contentType分组
        content_types = {}
        for event in message_events:
            content_type = event.get('data', {}).get('contentType', 'unknown')
            if content_type not in content_types:
                content_types[content_type] = []
            content_types[content_type].append(event)
        
        for content_type, events in content_types.items():
            print(f"\n  {content_type}: {len(events)}个事件")
            
            # 合并同类型事件的内容
            combined_content = "".join([e.get('content', '') for e in events])
            
            print(f"    合并内容长度: {len(combined_content)}")
            print(f"    包含换行符: {'\\n' in combined_content}")
            print(f"    换行符数量: {combined_content.count(chr(10))}")
            print(f"    包含Markdown标题: {'##' in combined_content}")
            print(f"    包含Markdown粗体: {'**' in combined_content}")
            
            if combined_content:
                print(f"    内容预览: '{combined_content[:200]}...'")
                
                # 检查是否是thinking内容
                thinking_indicators = ['我来', '分析', '建议', '梳理', '需求']
                has_thinking = any(indicator in combined_content for indicator in thinking_indicators)
                print(f"    疑似thinking内容: {has_thinking}")
    
    print(f"\n🎯 最终结论:")
    
    if accumulated_content:
        print(f"✅ 检测到thinking内容")
        print(f"   累积内容长度: {len(accumulated_content)}")
        print(f"   包含格式: {'##' in accumulated_content and '**' in accumulated_content}")
        
        print(f"\n📄 前端应该接收到的thinking内容:")
        print("=" * 40)
        print(accumulated_content)
        print("=" * 40)
        
    else:
        print(f"❌ 没有检测到thinking内容")
        print(f"   这说明后端确实存在过滤问题")

def main():
    """主函数"""
    print("开始直接测试流式接口...\n")
    
    try:
        # 运行异步测试
        asyncio.run(test_real_stream_api())
        print("\n🎉 流式接口测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
