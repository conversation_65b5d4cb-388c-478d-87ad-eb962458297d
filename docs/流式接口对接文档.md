# 营销系统流式接口对接文档

## 📋 概述

本文档描述了营销系统流式接口的使用方法，包括接口规范、事件类型、数据结构和前端集成示例。

## 🚀 接口信息

### 基础信息
- **接口地址**: `POST /api/marketing/chat/stream`
- **协议**: HTTP/1.1 + Server-Sent Events (SSE)
- **内容类型**: `text/event-stream`
- **编码**: UTF-8

### 请求格式

#### 请求头
```http
POST /api/marketing/chat/stream HTTP/1.1
Host: your-domain.com
Content-Type: application/json
Accept: text/event-stream
```

#### 请求体
```json
{
  "userInput": "我想做一个大学生促销活动，预算3万元",
  "userId": "user_123",
  "conversationId": "conv_456"
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userInput | string | ✅ | 用户输入的营销需求描述 |
| userId | string | ✅ | 用户唯一标识 |
| conversationId | string | ❌ | 对话ID，用于会话管理 |

## 📡 响应格式

### SSE 数据格式
```
data: {"event":"workflowStarted","messageId":"msg_abc123","conversationId":"conv_456","content":"开始执行营销工作流","data":{"totalSteps":5,"workflowType":"marketing"},"createdAt":1705395332}

data: {"event":"message","messageId":"msg_abc123","conversationId":"conv_456","content":"根据您的需求，我来为您分析...","data":null,"createdAt":1705395333}
```

### 统一事件结构
所有事件都遵循以下统一结构：

```typescript
interface StreamEvent {
  event: string;           // 事件类型
  messageId: string;       // 消息ID
  conversationId: string;  // 对话ID
  content: string;         // 用户可读的文本内容
  data: object | null;     // 结构化数据（程序处理用）
  createdAt: number;       // 创建时间戳
}
```

## 🎯 事件类型详解

### 1. workflowStarted - 工作流开始
```json
{
  "event": "workflowStarted",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "开始执行营销工作流",
  "data": {
    "totalSteps": 5,
    "workflowType": "marketing"
  },
  "createdAt": 1705395332
}
```

### 2. nodeStarted - 节点开始执行
```json
{
  "event": "nodeStarted",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "开始执行意图识别",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "stepNumber": 2,
    "totalSteps": 5,
    "progressPercent": 40,
    "displayName": "意图识别"
  },
  "createdAt": 1705395333
}
```

### 3. nodeFinished - 节点执行完成
```json
{
  "event": "nodeFinished",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "意图识别执行完成",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "status": "succeeded",
    "stepNumber": 2,
    "elapsedTime": 2.1,
    "outputs": {
      "goal": "促销活动",
      "audience": "大学生"
    },
    "displayName": "意图识别"
  },
  "createdAt": 1705395335
}
```

### 4. message - LLM文本流（打字机效果）
```json
{
  "event": "message",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "根据您的需求，我建议",
  "data": null,
  "createdAt": 1705395336
}
```

### 5. businessInsight - 业务洞察
```json
{
  "event": "businessInsight",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "意图识别分析结果",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "insightType": "intentionAnalysis",
    "intentType": "promotion",
    "confidence": 0.85,
    "targetAudience": "大学生",
    "keyInsights": ["目标群体明确", "促销意图清晰"],
    "displayName": "意图识别"
  },
  "createdAt": 1705395337
}
```

### 6. userGuidance - 用户引导
```json
{
  "event": "userGuidance",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "为了制定更精准的方案，还需要了解预算范围",
  "data": {
    "guidanceType": "conversational",
    "missingFields": ["budget", "timeline"],
    "interactionData": {
      "suggestedResponses": ["预算1万以下", "预算1-5万", "预算5万以上"],
      "formFields": []
    }
  },
  "createdAt": 1705395338
}
```

### 7. workflowFinished - 工作流结束
```json
{
  "event": "workflowFinished",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "工作流执行完成",
  "data": {
    "status": "succeeded",
    "totalSteps": 5,
    "completedSteps": 5,
    "elapsedTime": 12.5,
    "successRate": 1.0
  },
  "createdAt": 1705395350
}
```

### 8. messageEnd - 消息结束
```json
{
  "event": "messageEnd",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "回复生成完成",
  "data": {
    "totalEvents": 25,
    "executionTime": 12.5,
    "metadata": {
      "workflowInfo": {
        "totalEvents": 25,
        "executionTime": 12.5,
        "successRate": 1.0
      }
    }
  },
  "createdAt": 1705395351
}
```

### 9. error - 错误事件
```json
{
  "event": "error",
  "messageId": "msg_abc123",
  "conversationId": "conv_456",
  "content": "LLM调用超时，请重试",
  "data": {
    "errorCode": "LLM_TIMEOUT",
    "canRetry": true,
    "retryCount": 1,
    "maxRetries": 3,
    "status": 500
  },
  "createdAt": 1705395352
}
```

## 💻 前端集成示例

### JavaScript/TypeScript 示例

```javascript
// 发送流式请求
async function sendStreamRequest(userInput, userId, conversationId = '') {
  const response = await fetch('/api/marketing/chat/stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream'
    },
    body: JSON.stringify({
      userInput,
      userId,
      conversationId
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.slice(6));
            handleStreamEvent(eventData);
          } catch (e) {
            console.error('解析事件数据失败:', e);
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}

// 处理流式事件
function handleStreamEvent(eventData) {
  const { event, content, data, messageId, conversationId } = eventData;

  switch (event) {
    case 'workflowStarted':
      showWorkflowProgress(data.totalSteps);
      showMessage(content, 'system');
      break;

    case 'nodeStarted':
      updateProgress(data.progressPercent);
      showMessage(`🔄 ${content}`, 'system');
      break;

    case 'nodeFinished':
      const statusIcon = data.status === 'succeeded' ? '✅' : '❌';
      showMessage(`${statusIcon} ${content}`, 'system');
      break;

    case 'message':
      // 打字机效果
      appendToLastMessage(content);
      break;

    case 'businessInsight':
      showInsightCard(content, data);
      break;

    case 'userGuidance':
      showGuidanceUI(content, data);
      break;

    case 'workflowFinished':
      hideProgress();
      showMessage(`🎉 ${content}`, 'success');
      break;

    case 'messageEnd':
      showMessage(`✨ ${content}`, 'success');
      break;

    case 'error':
      showMessage(`❌ ${content}`, 'error');
      if (data.canRetry) {
        showRetryButton();
      }
      break;

    default:
      console.log('未知事件类型:', event);
  }
}
```

### React 示例

```jsx
import { useState, useCallback } from 'react';

function MarketingChat() {
  const [messages, setMessages] = useState([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [progress, setProgress] = useState(0);

  const sendMessage = useCallback(async (userInput) => {
    setIsStreaming(true);
    setProgress(0);

    try {
      const response = await fetch('/api/marketing/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
          userInput,
          userId: 'user_123',
          conversationId: 'conv_456'
        })
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const eventData = JSON.parse(line.slice(6));
            handleEvent(eventData);
          }
        }
      }
    } catch (error) {
      console.error('流式请求失败:', error);
    } finally {
      setIsStreaming(false);
    }
  }, []);

  const handleEvent = (eventData) => {
    const { event, content, data } = eventData;

    switch (event) {
      case 'nodeStarted':
        if (data.progressPercent) {
          setProgress(data.progressPercent);
        }
        break;

      case 'message':
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          if (lastMessage && lastMessage.type === 'ai') {
            return [
              ...prev.slice(0, -1),
              { ...lastMessage, content: lastMessage.content + content }
            ];
          } else {
            return [...prev, { type: 'ai', content }];
          }
        });
        break;

      case 'businessInsight':
        setMessages(prev => [...prev, {
          type: 'insight',
          content,
          data
        }]);
        break;

      // ... 其他事件处理
    }
  };

  return (
    <div className="marketing-chat">
      {/* 进度条 */}
      {isStreaming && (
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${progress}%` }}
          />
        </div>
      )}

      {/* 消息列表 */}
      <div className="messages">
        {messages.map((message, index) => (
          <div key={index} className={`message ${message.type}`}>
            {message.content}
            {message.data && (
              <div className="message-data">
                {JSON.stringify(message.data, null, 2)}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 输入框 */}
      <input 
        type="text" 
        onKeyPress={(e) => {
          if (e.key === 'Enter' && !isStreaming) {
            sendMessage(e.target.value);
            e.target.value = '';
          }
        }}
        disabled={isStreaming}
        placeholder="输入您的营销需求..."
      />
    </div>
  );
}
```

## 🔧 错误处理

### 常见错误码
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| LLM_TIMEOUT | LLM调用超时 | 可重试 |
| WORKFLOW_ERROR | 工作流执行错误 | 检查输入参数 |
| NETWORK_ERROR | 网络连接错误 | 检查网络连接 |
| RATE_LIMIT | 请求频率限制 | 稍后重试 |

### 重试机制
```javascript
async function sendWithRetry(userInput, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await sendStreamRequest(userInput);
      break;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 📝 注意事项

1. **连接管理**: 确保在组件卸载时关闭流式连接
2. **内存管理**: 及时释放 Reader 资源
3. **错误处理**: 实现完善的错误处理和重试机制
4. **用户体验**: 提供加载状态和进度指示
5. **数据验证**: 验证接收到的事件数据格式

## 🎯 最佳实践

1. **渐进式渲染**: 使用 `message` 事件实现打字机效果
2. **状态管理**: 根据 `nodeStarted/nodeFinished` 更新UI状态
3. **用户引导**: 处理 `userGuidance` 事件，提供交互式引导
4. **业务洞察**: 展示 `businessInsight` 数据，增强用户体验
5. **错误恢复**: 实现优雅的错误处理和恢复机制

## 🌐 CORS 配置

### 支持的域名
- 开发环境: `http://localhost:3000`
- 测试环境: `https://test.yourdomain.com`
- 生产环境: `https://yourdomain.com`

### 预检请求
对于跨域请求，浏览器会先发送 OPTIONS 预检请求：
```http
OPTIONS /api/marketing/chat/stream HTTP/1.1
Origin: http://localhost:3000
Access-Control-Request-Method: POST
Access-Control-Request-Headers: Content-Type
```

## 📊 性能优化建议

### 1. 连接池管理
```javascript
class StreamConnectionManager {
  constructor() {
    this.activeConnections = new Map();
    this.maxConnections = 5;
  }

  async createConnection(conversationId, userInput) {
    // 检查连接数限制
    if (this.activeConnections.size >= this.maxConnections) {
      throw new Error('连接数已达上限');
    }

    const controller = new AbortController();
    this.activeConnections.set(conversationId, controller);

    try {
      const response = await fetch('/api/marketing/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({ userInput, conversationId }),
        signal: controller.signal
      });

      return response;
    } catch (error) {
      this.activeConnections.delete(conversationId);
      throw error;
    }
  }

  closeConnection(conversationId) {
    const controller = this.activeConnections.get(conversationId);
    if (controller) {
      controller.abort();
      this.activeConnections.delete(conversationId);
    }
  }

  closeAllConnections() {
    for (const [id, controller] of this.activeConnections) {
      controller.abort();
    }
    this.activeConnections.clear();
  }
}
```

### 2. 事件缓冲优化
```javascript
class EventBuffer {
  constructor(flushInterval = 100) {
    this.buffer = [];
    this.flushInterval = flushInterval;
    this.timer = null;
  }

  addEvent(event) {
    this.buffer.push(event);

    if (!this.timer) {
      this.timer = setTimeout(() => {
        this.flush();
      }, this.flushInterval);
    }
  }

  flush() {
    if (this.buffer.length > 0) {
      const events = [...this.buffer];
      this.buffer = [];
      this.processEvents(events);
    }
    this.timer = null;
  }

  processEvents(events) {
    // 批量处理事件，提高性能
    events.forEach(event => handleStreamEvent(event));
  }
}
```

## 🔐 安全考虑

### 1. 输入验证
```javascript
function validateUserInput(userInput) {
  // 长度限制
  if (!userInput || userInput.length > 1000) {
    throw new Error('输入内容长度不符合要求');
  }

  // 内容过滤
  const forbiddenPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+=/i
  ];

  for (const pattern of forbiddenPatterns) {
    if (pattern.test(userInput)) {
      throw new Error('输入内容包含不安全字符');
    }
  }

  return userInput.trim();
}
```

### 2. 请求频率限制
```javascript
class RateLimiter {
  constructor(maxRequests = 10, timeWindow = 60000) {
    this.requests = [];
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  canMakeRequest() {
    const now = Date.now();
    // 清理过期请求
    this.requests = this.requests.filter(
      time => now - time < this.timeWindow
    );

    if (this.requests.length >= this.maxRequests) {
      return false;
    }

    this.requests.push(now);
    return true;
  }

  getWaitTime() {
    if (this.requests.length === 0) return 0;
    const oldestRequest = Math.min(...this.requests);
    return Math.max(0, this.timeWindow - (Date.now() - oldestRequest));
  }
}
```

## 🧪 测试示例

### 单元测试
```javascript
// Jest 测试示例
describe('StreamEventHandler', () => {
  let eventHandler;

  beforeEach(() => {
    eventHandler = new StreamEventHandler();
  });

  test('应该正确处理 workflowStarted 事件', () => {
    const event = {
      event: 'workflowStarted',
      content: '开始执行营销工作流',
      data: { totalSteps: 5 }
    };

    const result = eventHandler.handle(event);
    expect(result.type).toBe('workflow');
    expect(result.progress).toBe(0);
  });

  test('应该正确处理 message 事件', () => {
    const event = {
      event: 'message',
      content: '测试消息',
      data: null
    };

    const result = eventHandler.handle(event);
    expect(result.type).toBe('message');
    expect(result.content).toBe('测试消息');
  });
});
```

### 集成测试
```javascript
// Cypress 测试示例
describe('流式接口集成测试', () => {
  it('应该成功建立流式连接并接收事件', () => {
    cy.visit('/chat');

    // 输入测试消息
    cy.get('[data-testid="message-input"]')
      .type('我想做一个促销活动{enter}');

    // 验证工作流开始
    cy.contains('开始执行营销工作流').should('be.visible');

    // 验证进度更新
    cy.get('[data-testid="progress-bar"]').should('be.visible');

    // 验证消息接收
    cy.get('[data-testid="ai-message"]').should('contain.text', '根据您的需求');

    // 验证工作流完成
    cy.contains('工作流执行完成', { timeout: 30000 }).should('be.visible');
  });
});
```

## 📱 移动端适配

### React Native 示例
```javascript
import { EventSource } from 'react-native-sse';

class MobileStreamClient {
  constructor() {
    this.eventSource = null;
  }

  async startStream(userInput, userId) {
    const url = 'https://api.yourdomain.com/api/marketing/chat/stream';

    this.eventSource = new EventSource(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userInput, userId })
    });

    this.eventSource.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleEvent(data);
      } catch (error) {
        console.error('解析事件失败:', error);
      }
    });

    this.eventSource.addEventListener('error', (error) => {
      console.error('流式连接错误:', error);
      this.reconnect();
    });
  }

  handleEvent(eventData) {
    // 处理事件逻辑
    switch (eventData.event) {
      case 'message':
        this.updateUI(eventData.content);
        break;
      // ... 其他事件处理
    }
  }

  close() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
```

## 🔍 调试工具

### 开发者工具
```javascript
// 调试助手
class StreamDebugger {
  constructor() {
    this.events = [];
    this.startTime = Date.now();
  }

  logEvent(event) {
    const timestamp = Date.now() - this.startTime;
    const logEntry = {
      timestamp,
      event: event.event,
      content: event.content,
      dataSize: JSON.stringify(event.data || {}).length
    };

    this.events.push(logEntry);
    console.log(`[${timestamp}ms] ${event.event}: ${event.content}`);
  }

  getStats() {
    const eventCounts = {};
    let totalDataSize = 0;

    this.events.forEach(event => {
      eventCounts[event.event] = (eventCounts[event.event] || 0) + 1;
      totalDataSize += event.dataSize;
    });

    return {
      totalEvents: this.events.length,
      eventTypes: eventCounts,
      totalDataSize,
      duration: Date.now() - this.startTime
    };
  }

  exportLogs() {
    const blob = new Blob([JSON.stringify(this.events, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `stream-logs-${Date.now()}.json`;
    a.click();
  }
}
```

---
