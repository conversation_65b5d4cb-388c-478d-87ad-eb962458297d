#!/usr/bin/env python3
"""
测试内容类型修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow
from app.utils.stream_events import BusinessContentTypes

def test_content_type_fix():
    """测试内容类型修复"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试内容类型修复 ===\n")
    
    # 测试各个Agent的内容类型映射
    test_nodes = [
        "ROUTER_AGENT",
        "INTENTION_AGENT", 
        "TAG_CUSTOMER_AGENT",
        "PLAN_CREATOR_AGENT",
        "UNKNOWN_AGENT"
    ]
    
    print("节点内容类型映射:")
    for node in test_nodes:
        content_type = workflow._mapNodeToContentType(node)
        print(f"  {node} -> {content_type}")
        
        # 验证是否为thinking类型
        if content_type == BusinessContentTypes.THINKING_PROCESS:
            print(f"    ✅ 正确设置为thinking类型")
        else:
            print(f"    ❌ 未设置为thinking类型")
    
    print(f"\n新增的thinking内容类型: {BusinessContentTypes.THINKING_PROCESS}")
    
    # 模拟创建一个message事件
    print("\n模拟创建message事件:")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟thinking内容
    thinking_content = """## 我来分析一下您的需求

看到您想做一个**暑期促销活动**，这个目标很明确！

## 当前掌握的信息

从您的描述中，我能明确提取到的是活动的核心目标。"""
    
    # 模拟处理thinking内容
    filtered_content = workflow._filterThinkingContent(thinking_content)
    
    if filtered_content:
        print(f"✅ thinking内容被正确提取")
        print(f"   内容长度: {len(filtered_content)}")
        print(f"   包含Markdown: {'##' in filtered_content and '**' in filtered_content}")
        
        # 获取内容类型
        content_type = workflow._mapNodeToContentType("INTENTION_AGENT")
        print(f"   内容类型: {content_type}")
        
        # 模拟创建事件（简化版）
        from app.utils.stream_events import StreamEventBuilder
        
        event = StreamEventBuilder.createMessageEvent(
            messageId="test_msg_001",
            conversationId="test_conv_001", 
            content=filtered_content,
            contentType=content_type,
            nodeId="INTENTION_AGENT",
            runId="test_run_001"
        )
        
        print(f"\n生成的事件结构:")
        print(f"  event: {event['event']}")
        print(f"  contentType: {event['data']['contentType']}")
        print(f"  nodeId: {event['data']['nodeId']}")
        print(f"  content长度: {len(event['content'])}")
        print(f"  runId: {event.get('runId')}")
        
    else:
        print(f"❌ thinking内容未被提取")

def test_markdown_preservation():
    """测试Markdown格式保留"""
    
    workflow = MarketingWorkflow()
    
    print("\n=== 测试Markdown格式保留 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试各种Markdown格式
    markdown_samples = [
        "## 标题测试",
        "**粗体测试**",
        "- 列表项1\n- 列表项2",
        "1. 编号列表1\n2. 编号列表2",
        "`代码片段`",
        "普通文本\n\n换行测试"
    ]
    
    print("Markdown格式保留测试:")
    for i, sample in enumerate(markdown_samples):
        result = workflow._filterThinkingContent(sample)
        if result:
            preserved = result == sample
            print(f"  样本{i+1}: {'✅' if preserved else '❌'} '{sample[:20]}...'")
            if not preserved:
                print(f"    原始: '{sample}'")
                print(f"    结果: '{result}'")
        else:
            print(f"  样本{i+1}: ❌ 被过滤 '{sample[:20]}...'")

if __name__ == "__main__":
    print("开始测试内容类型修复...\n")
    
    try:
        test_content_type_fix()
        test_markdown_preservation()
        print("\n🎉 内容类型修复测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
