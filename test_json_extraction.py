#!/usr/bin/env python3
"""
测试JSON提取功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.IntentionAgent import IntentionAgent

def test_json_extraction():
    """测试JSON提取功能"""
    
    agent = IntentionAgent()
    
    print("=== 测试JSON提取功能 ===\n")
    
    # 测试用例1: 完整的thinking + JSON格式
    response1 = """<thinking>
## 我来帮您梳理需求

听您说想做一个**暑期促销活动**，这个主题非常契合季节特点，可以有效吸引大学生群体的关注。

## 当前掌握的信息

从您的描述中，我可以提取到以下核心要素：
- **活动目标**：虽然您提到的是"暑期促销活动"，但具体目标还需要进一步明确

## 我的专业建议

对于这样的活动，有几个关键点需要进一步确认...
</thinking>

{
  "goal": "暑期促销活动",
  "budget": "2万元",
  "audience": "大学生群体",
  "activityName": "",
  "period": "",
  "content": "",
  "incentive": "",
  "channels": "",
  "restriction": "",
  "missingCoreFields": ["goal"],
  "missingSecondaryFields": ["activityName", "period", "content", "incentive", "channels", "restriction"],
  "followupQuestions": {
    "goal": "暑期促销活动的具体目标是什么？"
  },
  "fieldSuggestions": {
    "goal": ["提升储蓄账户开户数", "促进信用卡申请", "增加理财产品购买量"]
  }
}"""
    
    try:
        json_content = agent._extractJsonFromResponse(response1)
        parsed_json = json.loads(json_content)
        print("测试1 - 完整格式:")
        print(f"✅ 成功提取JSON")
        print(f"   goal: {parsed_json.get('goal')}")
        print(f"   budget: {parsed_json.get('budget')}")
        print(f"   audience: {parsed_json.get('audience')}")
    except Exception as e:
        print(f"❌ 测试1失败: {e}")
    
    # 测试用例2: 带代码块的格式
    response2 = """<thinking>
分析过程...
</thinking>

```json
{
  "goal": "提升转化率",
  "budget": "10万元",
  "audience": "年轻用户"
}
```"""
    
    try:
        json_content = agent._extractJsonFromResponse(response2)
        parsed_json = json.loads(json_content)
        print("\n测试2 - 代码块格式:")
        print(f"✅ 成功提取JSON")
        print(f"   goal: {parsed_json.get('goal')}")
    except Exception as e:
        print(f"❌ 测试2失败: {e}")
    
    # 测试用例3: 只有JSON，没有thinking
    response3 = """{
  "goal": "测试目标",
  "budget": "5万元"
}"""
    
    try:
        json_content = agent._extractJsonFromResponse(response3)
        parsed_json = json.loads(json_content)
        print("\n测试3 - 纯JSON格式:")
        print(f"✅ 成功提取JSON")
        print(f"   goal: {parsed_json.get('goal')}")
    except Exception as e:
        print(f"❌ 测试3失败: {e}")
    
    # 测试用例4: 错误格式
    response4 = """<thinking>
只有thinking，没有JSON
</thinking>

这里没有JSON内容"""
    
    try:
        json_content = agent._extractJsonFromResponse(response4)
        print("\n测试4 - 错误格式:")
        print(f"❌ 应该失败但成功了: {json_content}")
    except Exception as e:
        print(f"\n测试4 - 错误格式:")
        print(f"✅ 正确识别错误: {e}")

if __name__ == "__main__":
    print("开始测试JSON提取功能...\n")
    
    try:
        test_json_extraction()
        print("\n🎉 JSON提取功能测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
