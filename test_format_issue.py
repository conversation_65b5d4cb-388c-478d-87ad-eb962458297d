#!/usr/bin/env python3
"""
测试格式化导致的问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_format_issue():
    """测试格式化导致的问题"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试格式化导致的问题 ===\n")
    
    # 测试thinking内容
    thinking_content = """## 我来帮您梳理需求

听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，尤其是针对大学生群体。

## 当前掌握的信息

从您的描述中，我可以提取到以下核心要素：

- **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用
- **预算**：2万元，这个金额可以支持一些线上推广活动
- **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群"""
    
    print("1. 测试_isBusinessContent判断")
    print("=" * 50)
    
    is_business = workflow._isBusinessContent(thinking_content)
    print(f"thinking内容被判断为业务内容: {is_business}")
    
    if is_business:
        print("包含的业务关键词:")
        business_keywords = [
            '路由', '决策', '意图', '分析', '客群', '方案', '评估', '用户', '目标',
            '状态', '置信度', '下一步', '节点', '紧急', '复杂', '推荐', '建议'
        ]
        found_keywords = [kw for kw in business_keywords if kw in thinking_content]
        for kw in found_keywords:
            print(f"  - {kw}")
    
    print("\n2. 测试格式化过程")
    print("=" * 50)
    
    # 测试格式化
    formatted_content = workflow._formatContentByNode(thinking_content, "INTENTION_AGENT", "businessInsight")
    
    print(f"原始内容长度: {len(thinking_content)}")
    print(f"格式化后长度: {len(formatted_content)}")
    print(f"内容是否改变: {thinking_content != formatted_content}")
    
    if thinking_content != formatted_content:
        print("\n内容变化对比:")
        print("原始内容前200字符:")
        print(repr(thinking_content[:200]))
        print("\n格式化后前200字符:")
        print(repr(formatted_content[:200]))
        
        # 检查换行符
        original_newlines = thinking_content.count('\n')
        formatted_newlines = formatted_content.count('\n')
        print(f"\n换行符数量: 原始={original_newlines}, 格式化后={formatted_newlines}")
    
    print("\n3. 测试完整的_formatBusinessContent流程")
    print("=" * 50)
    
    # 模拟token事件
    mock_token_event = {
        'content': thinking_content,
        'data': {
            'nodeId': 'INTENTION_AGENT',
            'contentType': 'businessInsight'
        },
        'messageId': 'test_msg',
        'conversationId': 'test_conv'
    }
    
    formatted_event = workflow._formatBusinessContent(mock_token_event)
    
    if formatted_event:
        final_content = formatted_event.get('content', '')
        print(f"最终内容长度: {len(final_content)}")
        print(f"最终内容换行符数量: {final_content.count(chr(10))}")
        print(f"事件内容是否改变: {thinking_content != final_content}")
        
        if thinking_content != final_content:
            print("\n最终内容变化:")
            print("前100字符对比:")
            print(f"原始: {repr(thinking_content[:100])}")
            print(f"最终: {repr(final_content[:100])}")
    else:
        print("格式化返回None")

def test_specific_formatting():
    """测试具体的格式化方法"""
    
    workflow = MarketingWorkflow()
    
    print("\n4. 测试具体的格式化方法")
    print("=" * 50)
    
    # 测试包含特定关键词的内容
    test_contents = [
        "## 我来分析一下您的需求",
        "从您的描述中，我可以分析到以下信息",
        "**目标客群**：大学生群体",
        "这是一个很好的营销方案建议",
        "用户的意图很明确"
    ]
    
    for i, content in enumerate(test_contents):
        print(f"\n测试内容{i+1}: '{content}'")
        
        # 检查是否被判断为业务内容
        is_business = workflow._isBusinessContent(content)
        print(f"  是否为业务内容: {is_business}")
        
        if is_business:
            # 格式化
            formatted = workflow._formatIntentionContent(content)
            print(f"  格式化前: '{content}'")
            print(f"  格式化后: '{formatted}'")
            print(f"  内容改变: {content != formatted}")

if __name__ == "__main__":
    print("开始测试格式化导致的问题...\n")
    
    try:
        test_format_issue()
        test_specific_formatting()
        print("\n🎉 格式化测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
