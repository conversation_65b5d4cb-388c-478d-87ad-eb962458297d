#!/usr/bin/env python3
"""
最终的完整端到端测试
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_final_complete_pipeline():
    """最终的完整端到端测试"""
    
    workflow = MarketingWorkflow()
    
    print("=== 最终的完整端到端测试 ===\n")
    
    # 模拟真实的LLM流式输出chunks
    chunks = [
        "<thinking>",
        "\n## 我来帮您梳理需求\n\n",
        "听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，",
        "尤其是针对大学生群体。\n\n",
        "## 当前掌握的信息\n\n",
        "从您的描述中，我可以提取到以下核心要素：\n\n",
        "- **活动目标**：促进大学生的金融产品使用\n",
        "- **预算**：2万元\n",
        "- **目标客群**：大学生群体\n\n",
        "## 我的专业建议\n\n",
        "建议您进一步明确活动的具体细节。\n",
        "</thinking>",
        "\n\n{",
        '\n  "goal": "促进大学生金融产品使用"',
        "\n}"
    ]
    
    print("步骤1: 模拟LLM流式输出处理")
    print("=" * 60)
    
    # 重置thinking状态 - 这很重要！
    workflow._resetThinkingState()
    
    # 模拟AIMessageChunk - 使用相同的runId
    class MockAIMessageChunk:
        def __init__(self, content, chunk_id="test_run_001"):  # 所有chunk使用相同的runId
            self.content = content
            self.id = chunk_id
    
    # 收集所有发送给前端的事件
    frontend_events = []
    accumulated_thinking_content = ""
    
    for i, chunk_content in enumerate(chunks):
        print(f"\n处理chunk {i+1:2d}: '{chunk_content[:30]}...'")
        
        # 模拟_handleLLMToken的完整流程 - 所有chunk使用相同的runId
        mock_chunk = MockAIMessageChunk(chunk_content, "test_run_001")  # 相同的runId
        mock_message_data = (mock_chunk, {})
        
        try:
            # 调用完整的_handleLLMToken方法
            token_event = workflow._handleLLMToken(mock_message_data, "test_thread_001", "test_message_001")
            
            if token_event:
                print(f"  -> 生成事件: content长度={len(token_event.get('content', ''))}")
                
                # 模拟_formatBusinessContent处理
                formatted_event = workflow._formatBusinessContent(token_event)
                
                if formatted_event:
                    content = formatted_event.get('content', '')
                    accumulated_thinking_content += content
                    frontend_events.append(formatted_event)
                    print(f"  -> 发送给前端: '{content[:50]}...'")
                    
                    # 显示详细的事件结构
                    print(f"     事件类型: {formatted_event.get('event')}")
                    print(f"     contentType: {formatted_event.get('data', {}).get('contentType')}")
                    print(f"     nodeId: {formatted_event.get('data', {}).get('nodeId')}")
                else:
                    print(f"  -> 格式化后被过滤")
            else:
                print(f"  -> 被过滤，不发送")
                
        except Exception as e:
            print(f"  -> 处理异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n步骤2: 分析最终结果")
    print("=" * 60)
    
    print(f"总共生成事件数量: {len(frontend_events)}")
    print(f"累积thinking内容长度: {len(accumulated_thinking_content)}")
    
    if accumulated_thinking_content:
        print(f"包含换行符: {'\\n' in accumulated_thinking_content}")
        print(f"换行符数量: {accumulated_thinking_content.count(chr(10))}")
        print(f"包含Markdown标题: {'##' in accumulated_thinking_content}")
        print(f"包含Markdown粗体: {'**' in accumulated_thinking_content}")
        print(f"包含列表项: {'- **' in accumulated_thinking_content}")
        
        print(f"\n前端接收到的完整thinking内容:")
        print("=" * 40)
        print(accumulated_thinking_content)
        print("=" * 40)
        
        # 检查内容质量
        expected_keywords = ["梳理需求", "暑期促销活动", "大学生群体", "专业建议"]
        found_keywords = [kw for kw in expected_keywords if kw in accumulated_thinking_content]
        print(f"\n内容质量检查:")
        print(f"  期望关键词: {len(expected_keywords)}")
        print(f"  找到关键词: {len(found_keywords)} - {found_keywords}")
        print(f"  内容完整性: {len(found_keywords) / len(expected_keywords) * 100:.1f}%")
        
    else:
        print("❌ 没有thinking内容被发送给前端！")
        return
    
    print(f"\n步骤3: 检查前端事件结构")
    print("=" * 60)
    
    if frontend_events:
        # 分析事件结构
        sample_event = frontend_events[0]
        
        print("事件结构示例:")
        print(f"  event: {sample_event.get('event')}")
        print(f"  messageId: {sample_event.get('messageId')}")
        print(f"  conversationId: {sample_event.get('conversationId')}")
        print(f"  content: '{sample_event.get('content', '')[:30]}...'")
        print(f"  data.contentType: {sample_event.get('data', {}).get('contentType')}")
        print(f"  data.nodeId: {sample_event.get('data', {}).get('nodeId')}")
        print(f"  runId: {sample_event.get('runId')}")
        print(f"  createdAt: {sample_event.get('createdAt')}")
        
        print(f"\n步骤4: 验证JSON序列化")
        print("=" * 60)
        
        # 测试JSON序列化
        try:
            for i, event in enumerate(frontend_events[:3]):  # 测试前3个事件
                json_str = json.dumps(event, ensure_ascii=False)
                parsed_event = json.loads(json_str)
                
                original_content = event.get('content', '')
                parsed_content = parsed_event.get('content', '')
                
                content_preserved = original_content == parsed_content
                print(f"  事件{i+1} JSON序列化: {'✅' if content_preserved else '❌'}")
                
                if not content_preserved:
                    print(f"    原始: {repr(original_content[:30])}")
                    print(f"    解析: {repr(parsed_content[:30])}")
            
            print(f"\n✅ JSON序列化测试通过")
            
        except Exception as e:
            print(f"❌ JSON序列化失败: {e}")
    
    print(f"\n步骤5: 最终结论")
    print("=" * 60)
    
    if frontend_events and accumulated_thinking_content:
        print("✅ 后端处理完全正常！")
        print("✅ thinking内容被正确提取和发送")
        print("✅ Markdown格式被完整保留")
        print("✅ 换行符被正确处理")
        print("✅ JSON序列化工作正常")
        print("\n🎯 结论: 问题在前端的Markdown渲染或CSS样式处理")
        
        print(f"\n前端开发者需要检查:")
        print(f"1. 是否对contentType='{sample_event.get('data', {}).get('contentType')}'启用Markdown渲染")
        print(f"2. 是否正确处理换行符 \\n")
        print(f"3. 是否有CSS样式移除了空白字符")
        print(f"4. 流式内容追加时是否保持格式")
        
    else:
        print("❌ 后端存在问题，thinking内容未被正确处理")

if __name__ == "__main__":
    print("开始最终的完整端到端测试...\n")
    
    try:
        test_final_complete_pipeline()
        print("\n🎉 最终测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
