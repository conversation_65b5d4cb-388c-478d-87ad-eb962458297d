#!/usr/bin/env python3
"""
完整的端到端测试：从LLM输出到前端接收
"""

import sys
import os
import json
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow
from app.utils.stream_events import StreamEventBuilder

def test_complete_pipeline():
    """完整的端到端测试"""
    
    workflow = MarketingWorkflow()
    
    print("=== 完整的端到端测试：从LLM输出到前端接收 ===\n")
    
    # 模拟真实的LLM输出（基于你提供的实际内容）
    real_llm_output = """<thinking>
## 我来帮您梳理需求

听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，尤其是针对大学生群体。大学生在暑假期间通常会有较多的闲暇时间和消费需求，比如购买电子产品、旅游出行或娱乐消费等，这为银行提供了很好的交叉销售机会。

您已经明确提到**目标客群是大学生群体**，并且**预算为2万元**，这是一个比较适中的预算范围，适合开展一些线上线下的联合促销活动。

## 当前掌握的信息

从您的描述中，我可以提取到以下核心要素：

- **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用，比如开通电子账户、办理信用卡或购买理财产品等。

- **预算**：2万元，这个金额可以支持一些线上推广活动，但可能不足以覆盖大规模线下宣传。

- **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群。

## 还需要补充的信息

尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：

1. **活动名称**：活动需要一个吸引人的名称，以便更好地传播和推广。

2. **活动时间**：暑期的具体时间段，例如6月1日-8月31日，这将直接影响活动的规划和执行。

3. **活动内容**：具体的促销形式是什么？比如满减优惠、抽奖活动还是赠送礼品？

4. **激励方式**：如何吸引大学生参与？是提供现金奖励、礼品兑换还是其他形式的激励？

5. **触达渠道**：通过哪些渠道进行宣传？比如社交媒体、校园海报还是合作商家？

6. **参与限制**：是否有年龄限制或其他参与条件？

## 我的专业建议

基于以上分析，我建议您进一步明确活动的具体细节。例如，可以通过线上平台（如微信公众号、抖音短视频）结合线下校园活动的形式，吸引更多大学生参与。同时，激励措施可以设计得有趣且实用，比如扫码注册即送电影票，或首单消费返现。

让我们一起完善这些细节，确保活动能够达到预期效果！
</thinking>

{
  "goal": "促进大学生金融产品使用",
  "budget": "2万元",
  "audience": "大学生群体",
  "activityName": "",
  "period": "",
  "content": "",
  "incentive": "",
  "channels": "",
  "restriction": "",
  "missingCoreFields": [],
  "missingSecondaryFields": ["activityName", "period", "content", "incentive", "channels", "restriction"],
  "followupQuestions": {
    "activityName": "活动名称是什么？",
    "period": "活动时间是什么时候？",
    "content": "活动具体内容是什么？",
    "incentive": "激励方式是什么？",
    "channels": "通过哪些渠道进行宣传？",
    "restriction": "参与限制有哪些？"
  },
  "fieldSuggestions": {
    "activityName": ["暑期金融狂欢节", "校园金融体验季", "大学生专属福利周"],
    "period": ["2024年6月1日-8月31日", "2024年7月1日-8月31日"],
    "content": ["扫码注册送礼", "首单消费返现", "满额抽奖"],
    "incentive": ["电影票", "校园周边礼品", "现金红包"],
    "channels": ["微信公众号", "抖音短视频", "校园海报", "合作商家"],
    "restriction": ["仅限在校大学生", "需完成实名认证"]
  }
}"""
    
    # 将LLM输出分成流式chunks（模拟真实的流式输出）
    chunks = [
        "<thinking>",
        "\n## 我来帮您梳理需求\n\n",
        "听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，",
        "尤其是针对大学生群体。大学生在暑假期间通常会有较多的闲暇时间和消费需求，",
        "比如购买电子产品、旅游出行或娱乐消费等，这为银行提供了很好的交叉销售机会。\n\n",
        "您已经明确提到**目标客群是大学生群体**，并且**预算为2万元**，",
        "这是一个比较适中的预算范围，适合开展一些线上线下的联合促销活动。\n\n",
        "## 当前掌握的信息\n\n",
        "从您的描述中，我可以提取到以下核心要素：\n\n",
        "- **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用，",
        "比如开通电子账户、办理信用卡或购买理财产品等。\n\n",
        "- **预算**：2万元，这个金额可以支持一些线上推广活动，但可能不足以覆盖大规模线下宣传。\n\n",
        "- **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群。\n\n",
        "## 还需要补充的信息\n\n",
        "尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：\n\n",
        "1. **活动名称**：活动需要一个吸引人的名称，以便更好地传播和推广。\n\n",
        "2. **活动时间**：暑期的具体时间段，例如6月1日-8月31日，",
        "这将直接影响活动的规划和执行。\n\n",
        "3. **活动内容**：具体的促销形式是什么？比如满减优惠、抽奖活动还是赠送礼品？\n\n",
        "4. **激励方式**：如何吸引大学生参与？是提供现金奖励、礼品兑换还是其他形式的激励？\n\n",
        "5. **触达渠道**：通过哪些渠道进行宣传？比如社交媒体、校园海报还是合作商家？\n\n",
        "6. **参与限制**：是否有年龄限制或其他参与条件？\n\n",
        "## 我的专业建议\n\n",
        "基于以上分析，我建议您进一步明确活动的具体细节。例如，可以通过线上平台（如微信公众号、抖音短视频）",
        "结合线下校园活动的形式，吸引更多大学生参与。同时，激励措施可以设计得有趣且实用，",
        "比如扫码注册即送电影票，或首单消费返现。\n\n",
        "让我们一起完善这些细节，确保活动能够达到预期效果！\n",
        "</thinking>",
        "\n\n{",
        '\n  "goal": "促进大学生金融产品使用",',
        '\n  "budget": "2万元",',
        '\n  "audience": "大学生群体"',
        "\n}"
    ]
    
    print("步骤1: 模拟LLM流式输出处理")
    print("=" * 60)
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟AIMessageChunk
    class MockAIMessageChunk:
        def __init__(self, content, chunk_id="test_chunk"):
            self.content = content
            self.id = chunk_id
    
    # 收集所有发送给前端的事件
    frontend_events = []
    accumulated_thinking_content = ""
    
    for i, chunk_content in enumerate(chunks):
        print(f"\n处理chunk {i+1:2d}: '{chunk_content[:30]}...'")
        
        # 模拟_handleLLMToken的完整流程
        mock_chunk = MockAIMessageChunk(chunk_content, f"chunk_{i+1}")
        mock_message_data = (mock_chunk, {})
        
        try:
            # 调用完整的_handleLLMToken方法
            token_event = workflow._handleLLMToken(mock_message_data, "test_thread_001", "test_message_001")
            
            if token_event:
                print(f"  -> 生成事件: content长度={len(token_event.get('content', ''))}")
                
                # 模拟_formatBusinessContent处理
                formatted_event = workflow._formatBusinessContent(token_event)
                
                if formatted_event:
                    content = formatted_event.get('content', '')
                    accumulated_thinking_content += content
                    frontend_events.append(formatted_event)
                    print(f"  -> 发送给前端: '{content[:50]}...'")
                else:
                    print(f"  -> 格式化后被过滤")
            else:
                print(f"  -> 被过滤，不发送")
                
        except Exception as e:
            print(f"  -> 处理异常: {e}")
    
    print(f"\n步骤2: 分析最终结果")
    print("=" * 60)
    
    print(f"总共生成事件数量: {len(frontend_events)}")
    print(f"累积thinking内容长度: {len(accumulated_thinking_content)}")
    print(f"包含换行符: {'\\n' in accumulated_thinking_content}")
    print(f"换行符数量: {accumulated_thinking_content.count(chr(10))}")
    print(f"包含Markdown标题: {'##' in accumulated_thinking_content}")
    print(f"包含Markdown粗体: {'**' in accumulated_thinking_content}")
    print(f"包含列表项: {'- **' in accumulated_thinking_content}")
    
    print(f"\n步骤3: 检查前端接收到的事件结构")
    print("=" * 60)
    
    if frontend_events:
        # 分析第一个和最后一个事件
        first_event = frontend_events[0]
        last_event = frontend_events[-1]
        
        print("第一个事件结构:")
        print(f"  event: {first_event.get('event')}")
        print(f"  messageId: {first_event.get('messageId')}")
        print(f"  conversationId: {first_event.get('conversationId')}")
        print(f"  content: '{first_event.get('content', '')[:50]}...'")
        print(f"  data.contentType: {first_event.get('data', {}).get('contentType')}")
        print(f"  data.nodeId: {first_event.get('data', {}).get('nodeId')}")
        print(f"  runId: {first_event.get('runId')}")
        print(f"  createdAt: {first_event.get('createdAt')}")
        
        print(f"\n最后一个事件结构:")
        print(f"  content: '{last_event.get('content', '')[:50]}...'")
        
        # 检查事件内容的完整性
        print(f"\n步骤4: 验证内容完整性")
        print("=" * 60)
        
        # 重新组装前端应该看到的完整内容
        frontend_complete_content = "".join([event.get('content', '') for event in frontend_events])
        
        print(f"前端完整内容长度: {len(frontend_complete_content)}")
        print(f"前端内容换行符数量: {frontend_complete_content.count(chr(10))}")
        
        # 与原始thinking内容对比
        original_thinking = real_llm_output[real_llm_output.find('<thinking>') + 10:real_llm_output.find('</thinking>')]
        
        print(f"原始thinking内容长度: {len(original_thinking)}")
        print(f"原始thinking换行符数量: {original_thinking.count(chr(10))}")
        
        content_match = frontend_complete_content.strip() == original_thinking.strip()
        print(f"内容完全匹配: {content_match}")
        
        if not content_match:
            print(f"\n内容差异分析:")
            print(f"前端内容前100字符: {repr(frontend_complete_content[:100])}")
            print(f"原始内容前100字符: {repr(original_thinking[:100])}")
        
        print(f"\n步骤5: 模拟前端JSON序列化")
        print("=" * 60)
        
        # 模拟前端接收到的完整事件列表
        frontend_json_events = []
        for event in frontend_events:
            try:
                # 模拟前端JSON序列化/反序列化
                json_str = json.dumps(event, ensure_ascii=False)
                parsed_event = json.loads(json_str)
                frontend_json_events.append(parsed_event)
            except Exception as e:
                print(f"JSON序列化失败: {e}")
        
        print(f"JSON序列化成功的事件数量: {len(frontend_json_events)}")
        
        if frontend_json_events:
            # 检查JSON序列化后的内容
            json_complete_content = "".join([event.get('content', '') for event in frontend_json_events])
            print(f"JSON序列化后内容长度: {len(json_complete_content)}")
            print(f"JSON序列化后换行符数量: {json_complete_content.count(chr(10))}")
            
            json_content_match = json_complete_content == frontend_complete_content
            print(f"JSON序列化前后内容一致: {json_content_match}")
            
            if not json_content_match:
                print(f"JSON序列化导致的差异:")
                print(f"序列化前: {repr(frontend_complete_content[:50])}")
                print(f"序列化后: {repr(json_complete_content[:50])}")

if __name__ == "__main__":
    print("开始完整的端到端测试...\n")
    
    try:
        test_complete_pipeline()
        print("\n🎉 完整测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
