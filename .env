
# 向量数据库配置
VECTOR_DB_PATH=./vector_db
SIMILARITY_THRESHOLD=0.75
MAX_RESULTS=20

# LLM配置
#LLM_MODEL=qwen-plus
LLM_MODEL=qwen-turbo-2025-02-11
LLM_TEMPERATURE=0.3
OPENAI_API_KEY=sk-b1d14899987d4bac9a888f85a3da02f7
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MAX_TOKENS=65535

# LangSmith配置
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT=https://api.smith.langchain.com
LANGSMITH_API_KEY=***************************************************
LANGSMITH_PROJECT=pr-respectful-grant-28


#USER_INFO_API_URL=https://testy.leagpoint.com/uum-cms-api/open/queryUserInfo
#
#BILL_INFO_API_URL=https://dev.leagpoint.com/inno_cms/gongdan/queryUserBillForLlm
#
#QUERY_BANK_LIST_API_URL=http://**************:10004/search/mcBank/queryBankList
#
#BANK_ACTIVITY_API_URL=http://**************:18099/clm/campaign/list
#QUERY_ACTIVITY_DETAIL_API_URL=http://**************:18099/clm/campaign/detail

# CORS跨域配置 - 允许所有来源（生产和测试环境通用）
CORS_ORIGINS=*

# 日志配置
LOG_DIR=/app/logs
LOG_LEVEL=DEBUG

# Redis配置
REDIS_HOST=h.1game.fun
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=liuyb
REDIS_TIMEOUT=5

# Checkpointer配置
CHECKPOINTER_MODE=redis
