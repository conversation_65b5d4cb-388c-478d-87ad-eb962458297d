#!/usr/bin/env python3
"""
调试过滤问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_debug_filtering():
    """调试过滤问题"""
    
    workflow = MarketingWorkflow()
    
    print("=== 调试过滤问题 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试一个简单的thinking chunk
    test_chunk = "## 我来帮您梳理需求\n\n听到您提到**暑期促销活动**"
    
    print(f"测试chunk: '{test_chunk}'")
    print("=" * 60)
    
    # 步骤1: 测试thinking过滤
    print("步骤1: 测试thinking过滤")
    filtered_content = workflow._filterThinkingContent(test_chunk)
    print(f"  _filterThinkingContent结果: '{filtered_content}'")
    print(f"  是否被过滤: {not filtered_content}")
    
    if not filtered_content:
        print("  ❌ thinking过滤失败！")
        return
    
    # 步骤2: 测试自然语言检查
    print(f"\n步骤2: 测试自然语言检查")
    import re
    has_natural_language = bool(re.search(r'[a-zA-Z\u4e00-\u9fff]', filtered_content))
    print(f"  包含自然语言: {has_natural_language}")
    
    if not has_natural_language:
        print("  ❌ 自然语言检查失败！")
        return
    
    # 步骤3: 测试工具调用检查
    print(f"\n步骤3: 测试工具调用检查")
    is_tool_call = workflow._isToolCallContent(filtered_content)
    print(f"  是否为工具调用: {is_tool_call}")
    
    if is_tool_call:
        print("  ❌ 被误判为工具调用！")
        return
    
    # 步骤4: 测试完整的_handleLLMToken流程
    print(f"\n步骤4: 测试完整的_handleLLMToken流程")
    
    class MockAIMessageChunk:
        def __init__(self, content, chunk_id="test_chunk"):
            self.content = content
            self.id = chunk_id
    
    mock_chunk = MockAIMessageChunk(test_chunk)
    mock_message_data = (mock_chunk, {})
    
    try:
        result = workflow._handleLLMToken(mock_message_data, "test_thread", "test_message")
        print(f"  _handleLLMToken结果: {result}")
        
        if result:
            print(f"  ✅ 成功生成事件")
            print(f"    content: '{result.get('content', '')}'")
            print(f"    data: {result.get('data', {})}")
        else:
            print(f"  ❌ _handleLLMToken返回None")
            
    except Exception as e:
        print(f"  ❌ _handleLLMToken异常: {e}")
        import traceback
        traceback.print_exc()

def test_thinking_state_issue():
    """测试thinking状态问题"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 测试thinking状态问题 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试thinking标签序列
    thinking_sequence = [
        "<thinking>",
        "## 我来分析",
        "</thinking>"
    ]
    
    print("测试thinking标签序列:")
    for i, chunk in enumerate(thinking_sequence):
        print(f"\n处理chunk {i+1}: '{chunk}'")
        
        # 检查状态
        print(f"  处理前状态:")
        print(f"    _thinking_buffer: '{getattr(workflow, '_thinking_buffer', 'NOT_SET')}'")
        print(f"    _in_thinking: {getattr(workflow, '_in_thinking', 'NOT_SET')}")
        
        # 处理
        result = workflow._filterThinkingContent(chunk)
        
        print(f"  处理结果: '{result}'")
        print(f"  处理后状态:")
        print(f"    _thinking_buffer: '{getattr(workflow, '_thinking_buffer', 'NOT_SET')}'")
        print(f"    _in_thinking: {getattr(workflow, '_in_thinking', 'NOT_SET')}")

def test_current_node_detection():
    """测试当前节点检测"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 测试当前节点检测 ===\n")
    
    # 测试_getCurrentExecutingNode方法
    current_node = workflow._getCurrentExecutingNode()
    print(f"当前执行节点: {current_node}")
    
    if current_node == "UNKNOWN_AGENT":
        print("❌ 节点检测失败，返回UNKNOWN_AGENT")
        print("这可能导致thinking内容被错误处理")
    else:
        print(f"✅ 节点检测成功: {current_node}")

def test_step_by_step_debug():
    """逐步调试"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 逐步调试 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟完整的thinking处理
    full_sequence = [
        "<thinking>",
        "## 我来分析\n\n",
        "这是思考内容",
        "</thinking>",
        "这是JSON内容"
    ]
    
    print("逐步处理完整序列:")
    accumulated = ""
    
    for i, chunk in enumerate(full_sequence):
        print(f"\n--- 处理步骤 {i+1} ---")
        print(f"输入chunk: '{chunk}'")
        
        # 模拟完整的处理流程
        class MockAIMessageChunk:
            def __init__(self, content, chunk_id="test_chunk"):
                self.content = content
                self.id = chunk_id
        
        mock_chunk = MockAIMessageChunk(chunk)
        mock_message_data = (mock_chunk, {})
        
        # 1. 提取内容
        content = chunk
        print(f"1. 提取内容: '{content}'")
        
        # 2. thinking过滤
        filtered_content = workflow._filterThinkingContent(content)
        print(f"2. thinking过滤: '{filtered_content}'")
        
        if not filtered_content:
            print("   -> 被thinking过滤器过滤")
            continue
        
        # 3. 自然语言检查
        import re
        has_natural = bool(re.search(r'[a-zA-Z\u4e00-\u9fff]', filtered_content))
        print(f"3. 自然语言检查: {has_natural}")
        
        if not has_natural:
            print("   -> 被自然语言检查过滤")
            continue
        
        # 4. 工具调用检查
        is_tool_call = workflow._isToolCallContent(filtered_content)
        print(f"4. 工具调用检查: {is_tool_call}")
        
        if is_tool_call:
            print("   -> 被工具调用检查过滤")
            continue
        
        print(f"   -> ✅ 通过所有检查，应该发送给前端")
        accumulated += filtered_content
    
    print(f"\n最终累积内容: '{accumulated}'")
    print(f"累积内容长度: {len(accumulated)}")

if __name__ == "__main__":
    print("开始调试过滤问题...\n")
    
    try:
        test_debug_filtering()
        test_thinking_state_issue()
        test_current_node_detection()
        test_step_by_step_debug()
        print("\n🎉 调试完成！")
        
    except Exception as e:
        print(f"\n❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
