#!/usr/bin/env python3
"""
简单的thinking过滤测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_simple_thinking():
    """简单的thinking过滤测试"""
    
    workflow = MarketingWorkflow()
    
    print("=== 简单的thinking过滤测试 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试序列
    test_sequence = [
        "<thinking>",
        "这是思考内容",
        "</thinking>"
    ]
    
    print("测试序列处理:")
    for i, chunk in enumerate(test_sequence):
        print(f"\n步骤{i+1}: 输入 '{chunk}'")
        
        # 检查状态
        buffer = getattr(workflow, '_thinking_buffer', 'NOT_SET')
        in_thinking = getattr(workflow, '_in_thinking', 'NOT_SET')
        print(f"  处理前状态: buffer='{buffer}', in_thinking={in_thinking}")
        
        # 处理
        result = workflow._filterThinkingContent(chunk)
        
        # 检查结果
        buffer_after = getattr(workflow, '_thinking_buffer', 'NOT_SET')
        in_thinking_after = getattr(workflow, '_in_thinking', 'NOT_SET')
        print(f"  处理结果: '{result}'")
        print(f"  处理后状态: buffer='{buffer_after}', in_thinking={in_thinking_after}")
        
        # 模拟_handleLLMToken
        if result:
            print(f"  -> 应该发送给前端: '{result}'")
        else:
            print(f"  -> 被过滤，不发送")

def test_combined_chunks():
    """测试组合chunks"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 测试组合chunks ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 测试组合chunk（模拟真实情况）
    combined_chunks = [
        "<thinking>\n## 标题\n\n内容",
        "更多内容\n</thinking>\nJSON内容"
    ]
    
    print("测试组合chunks:")
    for i, chunk in enumerate(combined_chunks):
        print(f"\n处理chunk{i+1}: '{chunk}'")
        result = workflow._filterThinkingContent(chunk)
        print(f"  结果: '{result}'")

def test_real_scenario():
    """测试真实场景"""
    
    workflow = MarketingWorkflow()
    
    print(f"\n=== 测试真实场景 ===\n")
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟真实的LLM输出chunks
    real_chunks = [
        "<thinking>",
        "\n## 我来分析\n\n",
        "这是分析内容",
        "</thinking>",
        "\n{\"goal\": \"test\"}"
    ]
    
    print("真实场景测试:")
    accumulated = ""
    
    for i, chunk in enumerate(real_chunks):
        print(f"\n处理chunk{i+1}: '{chunk}'")
        result = workflow._filterThinkingContent(chunk)
        print(f"  结果: '{result}'")
        
        if result:
            accumulated += result
            print(f"  累积: '{accumulated}'")

if __name__ == "__main__":
    print("开始简单的thinking过滤测试...\n")
    
    try:
        test_simple_thinking()
        test_combined_chunks()
        test_real_scenario()
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
