#!/usr/bin/env python3
"""
测试thinking内容显示问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.workflows.MarketingWorkflow import MarketingWorkflow

def test_thinking_display():
    """测试thinking内容显示问题"""
    
    workflow = MarketingWorkflow()
    
    print("=== 测试thinking内容显示问题 ===\n")
    
    # 模拟实际的LLM输出（基于你提供的内容）
    llm_output = """<thinking>
## 我来帮您梳理需求

听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，尤其是针对大学生群体。大学生在暑假期间通常会有较多的闲暇时间和消费需求，比如购买电子产品、旅游出行或娱乐消费等，这为银行提供了很好的交叉销售机会。

您已经明确提到**目标客群是大学生群体**，并且**预算为2万元**，这是一个比较适中的预算范围，适合开展一些线上线下的联合促销活动。

## 当前掌握的信息

从您的描述中，我可以提取到以下核心要素：

- **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用，比如开通电子账户、办理信用卡或购买理财产品等。

- **预算**：2万元，这个金额可以支持一些线上推广活动，但可能不足以覆盖大规模线下宣传。

- **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群。

## 还需要补充的信息

尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：

1. **活动名称**：活动需要一个吸引人的名称，以便更好地传播和推广。

2. **活动时间**：暑期的具体时间段，例如6月1日-8月31日，这将直接影响活动的规划和执行。

3. **活动内容**：具体的促销形式是什么？比如满减优惠、抽奖活动还是赠送礼品？

4. **激励方式**：如何吸引大学生参与？是提供现金奖励、礼品兑换还是其他形式的激励？

5. **触达渠道**：通过哪些渠道进行宣传？比如社交媒体、校园海报还是合作商家？

6. **参与限制**：是否有年龄限制或其他参与条件？

## 我的专业建议

基于以上分析，我建议您进一步明确活动的具体细节。例如，可以通过线上平台（如微信公众号、抖音短视频）结合线下校园活动的形式，吸引更多大学生参与。同时，激励措施可以设计得有趣且实用，比如扫码注册即送电影票，或首单消费返现。

让我们一起完善这些细节，确保活动能够达到预期效果！
</thinking>

{
  "goal": "促进大学生金融产品使用",
  "budget": "2万元",
  "audience": "大学生群体",
  "activityName": "",
  "period": "",
  "content": "",
  "incentive": "",
  "channels": "",
  "restriction": "",
  "missingCoreFields": [],
  "missingSecondaryFields": ["activityName", "period", "content", "incentive", "channels", "restriction"],
  "followupQuestions": {
    "activityName": "活动名称是什么？",
    "period": "活动时间是什么时候？",
    "content": "活动具体内容是什么？",
    "incentive": "激励方式是什么？",
    "channels": "通过哪些渠道进行宣传？",
    "restriction": "参与限制有哪些？"
  },
  "fieldSuggestions": {
    "activityName": ["暑期金融狂欢节", "校园金融体验季", "大学生专属福利周"],
    "period": ["2024年6月1日-8月31日", "2024年7月1日-8月31日"],
    "content": ["扫码注册送礼", "首单消费返现", "满额抽奖"],
    "incentive": ["电影票", "校园周边礼品", "现金红包"],
    "channels": ["微信公众号", "抖音短视频", "校园海报", "合作商家"],
    "restriction": ["仅限在校大学生", "需完成实名认证"]
  }
}"""
    
    # 重置thinking状态
    workflow._resetThinkingState()
    
    # 模拟流式处理
    print("模拟流式处理过程:")
    print("=" * 50)
    
    # 将输出分成小块来模拟流式处理
    chunks = [
        "<thinking>",
        "\n## 我来帮您梳理需求\n\n",
        "听到您提到**暑期促销活动**，首先让我想到这是一个非常有潜力的营销机会，",
        "尤其是针对大学生群体。大学生在暑假期间通常会有较多的闲暇时间和消费需求，",
        "比如购买电子产品、旅游出行或娱乐消费等，这为银行提供了很好的交叉销售机会。\n\n",
        "您已经明确提到**目标客群是大学生群体**，并且**预算为2万元**，",
        "这是一个比较适中的预算范围，适合开展一些线上线下的联合促销活动。\n\n",
        "## 当前掌握的信息\n\n",
        "从您的描述中，我可以提取到以下核心要素：\n\n",
        "- **活动目标**：虽然您没有明确说明具体目标，但从背景推测可能是促进大学生的金融产品使用，",
        "比如开通电子账户、办理信用卡或购买理财产品等。\n\n",
        "- **预算**：2万元，这个金额可以支持一些线上推广活动，但可能不足以覆盖大规模线下宣传。\n\n",
        "- **目标客群**：大学生群体，这是一个非常明确且具有针对性的客群。\n\n",
        "## 还需要补充的信息\n\n",
        "尽管目前掌握的信息已经较为清晰，但还有一些关键细节需要进一步确认：\n\n",
        "1. **活动名称**：活动需要一个吸引人的名称，以便更好地传播和推广。\n\n",
        "2. **活动时间**：暑期的具体时间段，例如6月1日-8月31日，",
        "这将直接影响活动的规划和执行。\n\n",
        "</thinking>",
        "\n\n{",
        '\n  "goal": "促进大学生金融产品使用",',
        '\n  "budget": "2万元"',
        "\n}"
    ]
    
    accumulated_output = ""
    
    for i, chunk in enumerate(chunks):
        print(f"\n--- 处理块 {i+1}: '{chunk[:30]}...' ---")
        
        result = workflow._filterThinkingContent(chunk)
        
        if result:
            accumulated_output += result
            print(f"输出: '{result[:50]}...'")
        else:
            print("输出: (被过滤)")
    
    print("\n" + "=" * 50)
    print("最终累积输出:")
    print("=" * 50)
    print(accumulated_output)
    
    print("\n" + "=" * 50)
    print("分析问题:")
    print("=" * 50)
    
    # 检查是否保留了换行符和格式
    if "\n" in accumulated_output:
        print("✅ 换行符被保留")
    else:
        print("❌ 换行符丢失")
    
    if "##" in accumulated_output:
        print("✅ Markdown标题被保留")
    else:
        print("❌ Markdown标题丢失")
    
    if "**" in accumulated_output:
        print("✅ Markdown粗体被保留")
    else:
        print("❌ Markdown粗体丢失")

if __name__ == "__main__":
    print("开始测试thinking内容显示问题...\n")
    
    try:
        test_thinking_display()
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
